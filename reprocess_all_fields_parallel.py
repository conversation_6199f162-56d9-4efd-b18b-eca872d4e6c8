#!/usr/bin/env python3
"""
Multi-threaded script to reprocess rental listings and extract ALL available fields.

This script:
1. Finds all active rental listings with missing data
2. Re-fetches listing pages using 5 parallel threads
3. Extracts ALL available information from JSON responses
4. Updates the database with comprehensive data

Usage:
    python reprocess_all_fields_parallel.py
    python reprocess_all_fields_parallel.py --limit 100  # Process only first 100 listings
    python reprocess_all_fields_parallel.py --threads 3  # Use 3 threads instead of 5
"""

import logging
import time
import random
import sys
import threading
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from queue import Queue

from db.db_setup import get_db_connection
from db.rental_db_operations import update_listing_comprehensive
from scraper.rental_fetch_and_parse import fetch_page, download_rental_data_from_listing_page
from scraper.rental_transform_data import transform_rental_data
from config.logging_config import setup_logger

# Setup logging with thread safety
logger = setup_logger()

# Thread-safe counters
class ThreadSafeCounters:
    def __init__(self):
        self.lock = threading.Lock()
        self.successful_updates = 0
        self.failed_updates = 0
        self.total_fields_extracted = 0
        self.processed_count = 0
    
    def increment_success(self, fields_count):
        with self.lock:
            self.successful_updates += 1
            self.total_fields_extracted += fields_count
            self.processed_count += 1
    
    def increment_failure(self):
        with self.lock:
            self.failed_updates += 1
            self.processed_count += 1
    
    def get_stats(self):
        with self.lock:
            return {
                'successful': self.successful_updates,
                'failed': self.failed_updates,
                'total_fields': self.total_fields_extracted,
                'processed': self.processed_count
            }

def get_listings_with_missing_data(cur, limit=None):
    """Get all active rental listings that have missing critical data"""
    try:
        limit_clause = f"LIMIT {limit}" if limit else ""
        
        query = f"""
            SELECT id, otodom_listing_id, offer_link, area, rental_price, rooms_num, 
                   floor_num, heating, building_build_year
            FROM apartments_rental_listings
            WHERE active = true 
            AND (area IS NULL OR rental_price IS NULL OR rooms_num IS NULL 
                 OR floor_num IS NULL OR heating IS NULL OR building_build_year IS NULL)
            ORDER BY id
            {limit_clause}
            ;"""
        
        cur.execute(query)
        results = cur.fetchall()
        
        logging.info(f"Znaleziono {len(results)} aktywnych ofert z brakującymi danymi")
        return results
        
    except Exception as error:
        logging.exception(f"Błąd podczas pobierania ofert z brakującymi danymi: {error}")
        return []


def process_single_listing_thread_safe(listing_info, thread_id, counters, progress_queue):
    """
    Process a single listing in a thread-safe manner.
    
    Args:
        listing_info (tuple): Listing data
        thread_id (int): Thread identifier
        counters (ThreadSafeCounters): Thread-safe counters
        progress_queue (Queue): Queue for progress reporting
        
    Returns:
        dict: Results with success status and extracted fields count
    """
    db_id, otodom_listing_id, offer_link = listing_info[0], listing_info[1], listing_info[2]
    
    # Create separate database connection for this thread
    conn = get_db_connection()
    if not conn:
        logging.error(f"[Thread-{thread_id}] ❌ Nie udało się połączyć z bazą danych dla oferty ID {db_id}")
        counters.increment_failure()
        progress_queue.put(f"[Thread-{thread_id}] ❌ DB connection failed for ID {db_id}")
        return {'success': False, 'error': 'db_connection_failed', 'extracted_fields': 0}
    
    cur = conn.cursor()
    
    try:
        progress_queue.put(f"[Thread-{thread_id}] 🔄 Processing ID {db_id} (Otodom: {otodom_listing_id})")
        
        # Add small random delay to avoid overwhelming the server
        delay = random.uniform(0.5, 1.5)
        time.sleep(delay)
        
        # Fetch the listing page
        response = fetch_page(offer_link)
        if response is None:
            logging.error(f"[Thread-{thread_id}] ❌ Nie udało się pobrać strony dla oferty ID {db_id}")
            counters.increment_failure()
            progress_queue.put(f"[Thread-{thread_id}] ❌ Fetch failed for ID {db_id}")
            return {'success': False, 'error': 'fetch_failed', 'extracted_fields': 0}
        
        # Extract data from the page
        offer_data = download_rental_data_from_listing_page(response)
        if offer_data is None:
            logging.error(f"[Thread-{thread_id}] ❌ Nie udało się wyodrębnić danych z oferty ID {db_id}")
            counters.increment_failure()
            progress_queue.put(f"[Thread-{thread_id}] ❌ Extraction failed for ID {db_id}")
            return {'success': False, 'error': 'extraction_failed', 'extracted_fields': 0}
        
        # Transform the data
        transformed_data = transform_rental_data(offer_data)
        if transformed_data is None:
            logging.error(f"[Thread-{thread_id}] ❌ Nie udało się przetworzyć danych dla oferty ID {db_id}")
            counters.increment_failure()
            progress_queue.put(f"[Thread-{thread_id}] ❌ Transform failed for ID {db_id}")
            return {'success': False, 'error': 'transform_failed', 'extracted_fields': 0}
        
        # Prepare data for update
        update_data = {}
        field_names = [
            'area', 'rental_price', 'rental_price_per_m', 'rooms_num', 'floor_num',
            'heating', 'ownership', 'construction_status', 'energy_certificate',
            'building_build_year', 'building_floors_num', 'building_material',
            'building_type', 'windows_type', 'deposit_amount', 'additional_rent', 'features'
        ]
        
        for field in field_names:
            value = transformed_data.get(field)
            if value is not None:
                # Special handling for different field types
                if isinstance(value, str) and value.strip() == '':
                    continue
                elif isinstance(value, (int, float)) and field in ['floor_num', 'rooms_num', 'building_floors_num']:
                    update_data[field] = value
                elif isinstance(value, (int, float)) and value == 0:
                    continue
                else:
                    update_data[field] = value
        
        if not update_data:
            logging.warning(f"[Thread-{thread_id}] ⚠️ Brak nowych danych dla oferty ID {db_id}")
            counters.increment_failure()
            progress_queue.put(f"[Thread-{thread_id}] ⚠️ No data for ID {db_id}")
            return {'success': False, 'error': 'no_data', 'extracted_fields': 0}
        
        # Update the database
        success = update_listing_comprehensive(db_id, update_data, conn, cur)
        if success:
            extracted_count = len(update_data)
            counters.increment_success(extracted_count)
            
            # Log key extracted values
            key_fields = ['area', 'rental_price', 'rooms_num', 'floor_num', 'heating', 'building_build_year']
            extracted_summary = []
            for field in key_fields:
                if field in update_data:
                    extracted_summary.append(f"{field}={update_data[field]}")
            
            progress_msg = f"[Thread-{thread_id}] ✅ ID {db_id}: {extracted_count} fields"
            if extracted_summary:
                progress_msg += f" ({', '.join(extracted_summary[:3])}...)"
            
            progress_queue.put(progress_msg)
            logging.info(f"[Thread-{thread_id}] ✅ Oferta ID {db_id} zaktualizowana: {extracted_count} pól")
            
            return {'success': True, 'error': None, 'extracted_fields': extracted_count}
        else:
            logging.error(f"[Thread-{thread_id}] ❌ Nie udało się zaktualizować oferty ID {db_id}")
            counters.increment_failure()
            progress_queue.put(f"[Thread-{thread_id}] ❌ Update failed for ID {db_id}")
            return {'success': False, 'error': 'update_failed', 'extracted_fields': 0}
            
    except Exception as error:
        logging.exception(f"[Thread-{thread_id}] ❌ Nieoczekiwany błąd podczas przetwarzania oferty ID {db_id}: {error}")
        counters.increment_failure()
        progress_queue.put(f"[Thread-{thread_id}] ❌ Error for ID {db_id}: {str(error)[:50]}...")
        return {'success': False, 'error': 'unexpected_error', 'extracted_fields': 0}
    
    finally:
        # Close database connection for this thread
        if cur:
            cur.close()
        if conn:
            conn.close()


def progress_monitor(progress_queue, total_listings, counters):
    """Monitor and display progress from all threads"""
    last_update = time.time()
    
    while True:
        try:
            # Get message from queue (with timeout)
            message = progress_queue.get(timeout=1.0)
            print(message)
            
            # Print summary every 10 seconds
            current_time = time.time()
            if current_time - last_update >= 10.0:
                stats = counters.get_stats()
                processed = stats['processed']
                successful = stats['successful']
                failed = stats['failed']
                total_fields = stats['total_fields']
                
                if processed > 0:
                    progress_pct = (processed / total_listings) * 100
                    avg_fields = total_fields / successful if successful > 0 else 0
                    print(f"\n📊 PROGRESS: {processed}/{total_listings} ({progress_pct:.1f}%) | "
                          f"✅ {successful} success | ❌ {failed} failed | "
                          f"📈 {total_fields} fields ({avg_fields:.1f} avg)\n")
                
                last_update = current_time
            
            progress_queue.task_done()
            
        except:
            # Timeout or queue empty - check if we should continue
            stats = counters.get_stats()
            if stats['processed'] >= total_listings:
                break
            continue


def main():
    """Main function for parallel reprocessing"""
    
    # Parse command line arguments
    limit = None
    num_threads = 5  # Default number of threads
    
    if '--limit' in sys.argv:
        try:
            limit_index = sys.argv.index('--limit')
            limit = int(sys.argv[limit_index + 1])
            print(f"🔢 Ograniczenie: przetwarzanie maksymalnie {limit} ofert")
        except (IndexError, ValueError):
            print("❌ Błędny format parametru --limit. Użyj: --limit 100")
            return
    
    if '--threads' in sys.argv:
        try:
            threads_index = sys.argv.index('--threads')
            num_threads = int(sys.argv[threads_index + 1])
            if num_threads < 1 or num_threads > 10:
                print("❌ Liczba wątków musi być między 1 a 10")
                return
            print(f"🧵 Używam {num_threads} wątków")
        except (IndexError, ValueError):
            print("❌ Błędny format parametru --threads. Użyj: --threads 3")
            return
    
    print("="*80)
    print("PARALLEL COMPREHENSIVE REPROCESSING OF RENTAL LISTINGS")
    print("="*80)
    print(f"🧵 Threads: {num_threads}")
    print(f"🔢 Limit: {limit if limit else 'No limit'}")
    print("="*80)
    
    start_time = datetime.now()
    logging.info(f"🚀 Rozpoczynam równoległy reprocessing z {num_threads} wątkami...")
    
    # Connect to database for initial query
    conn = get_db_connection()
    if not conn:
        logging.error("❌ Nie udało się połączyć z bazą danych")
        return
    
    cur = conn.cursor()
    
    try:
        # Get all listings with missing data
        logging.info("🔍 Wyszukuję oferty z brakującymi danymi...")
        listings_with_missing_data = get_listings_with_missing_data(cur, limit)
        
        if not listings_with_missing_data:
            logging.info("✅ Brak ofert z brakującymi danymi - wszystko w porządku!")
            return
        
        total_listings = len(listings_with_missing_data)
        logging.info(f"📊 Znaleziono {total_listings} ofert do przetworzenia")
        
        # Initialize thread-safe counters and progress queue
        counters = ThreadSafeCounters()
        progress_queue = Queue()
        
        # Start progress monitor in separate thread
        progress_thread = threading.Thread(
            target=progress_monitor, 
            args=(progress_queue, total_listings, counters),
            daemon=True
        )
        progress_thread.start()
        
        # Process listings in parallel
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            # Submit all tasks
            future_to_listing = {
                executor.submit(
                    process_single_listing_thread_safe, 
                    listing_info, 
                    i % num_threads + 1,  # Thread ID
                    counters,
                    progress_queue
                ): listing_info 
                for i, listing_info in enumerate(listings_with_missing_data)
            }
            
            # Wait for all tasks to complete
            for future in as_completed(future_to_listing):
                listing_info = future_to_listing[future]
                try:
                    result = future.result()
                except Exception as exc:
                    db_id = listing_info[0]
                    logging.exception(f'❌ Listing ID {db_id} generated an exception: {exc}')
                    counters.increment_failure()
        
        # Final summary
        end_time = datetime.now()
        duration = end_time - start_time
        stats = counters.get_stats()
        
        successful_updates = stats['successful']
        failed_updates = stats['failed']
        total_fields_extracted = stats['total_fields']
        avg_fields_per_listing = total_fields_extracted / successful_updates if successful_updates > 0 else 0
        
        print("\n" + "="*80)
        print("PODSUMOWANIE RÓWNOLEGŁEGO REPROCESSINGU")
        print("="*80)
        print(f"🧵 Wątki:                       {num_threads}")
        print(f"📊 Łącznie przetworzono:        {total_listings}")
        print(f"✅ Pomyślnie zaktualizowano:    {successful_updates}")
        print(f"❌ Błędy:                       {failed_updates}")
        print(f"📈 Łącznie wyodrębnionych pól:  {total_fields_extracted}")
        print(f"📊 Średnio pól na ofertę:       {avg_fields_per_listing:.1f}")
        print(f"⏱️ Czas wykonania:              {duration}")
        print(f"🚀 Przyspieszenie:              ~{num_threads}x szybciej niż sekwencyjnie")
        print("="*80)
        
        logging.info(f"🎉 Równoległy reprocessing zakończony!")
        logging.info(f"📊 Statystyki: {successful_updates} sukces, {failed_updates} błędów z {total_listings} łącznie")
        logging.info(f"📈 Wyodrębniono łącznie {total_fields_extracted} pól danych")
        logging.info(f"⏱️ Czas wykonania: {duration}")
        
    except Exception as error:
        logging.exception(f"❌ Nieoczekiwany błąd podczas równoległego reprocessingu: {error}")
    
    finally:
        # Close main database connection
        if cur:
            cur.close()
        if conn:
            conn.close()
        logging.info("🔌 Główne połączenie z bazą danych zamknięte")


if __name__ == "__main__":
    main()
