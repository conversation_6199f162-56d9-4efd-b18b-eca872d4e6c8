import requests, json, time, random, logging
from bs4 import BeautifulSoup
from datetime import datetime


def fetch_page(url: str) -> requests.Response | None:
    """
    Fetches a web page from the given URL with appropriate headers and delay.
    
    This function sends a GET request to the specified URL with headers that mimic a real browser.
    It includes a random delay between 1-2 seconds to avoid being blocked by the server.

    Args:
        url (str): The URL of the page to fetch

    Returns:
        requests.Response | None: The response object if successful (status code 200), 
                                 None if the request fails

    If the request is successful (status code 200), it returns the response object.
    Otherwise, it prints an error message with the status code and returns None.
    """
    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache",
            "Accept-Language": "pl-PL,pl;q=0.9"
        }

        html_response = requests.get(url, headers=headers)
        time.sleep(random.uniform(1,2))

        if html_response.status_code == 200:
            return html_response
        else: 
            logging.error(f"Błąd HTTP podczas pobierania strony ({url}): {html_response.status_code}")
            return None
    except Exception as error:
        logging.exception(f"Błąd podczas pobierania strony {url}: {error}")
        return None


def download_rental_data_page_by_page(base_url: str):
    """
    Generator that yields rental listing information page by page from otodom.com search results.

    This function processes one page at a time, yielding basic information about each rental listing
    (ID, area, price, price_per_m and link) for immediate processing instead of collecting all pages first.

    Args:
        base_url (str): The base search URL for rental apartments (without the `&page=` parameter)

    Yields:
        tuple: (page_number, total_pages, offers_on_page) where:
            - page_number (int): Current page number being processed
            - total_pages (int): Total number of pages available
            - offers_on_page (list): List of offer dictionaries for this page

    Raises:
        Exception: If the first page fails to load, or if parsing fails due to a missing or incorrect script tag
        ValueError: If the script tag does not contain the expected data structure
    """
    try:
        # Pobierz pierwszą stronę, żeby sprawdzić ile jest stron w sumie
        first_page_url = f"{base_url}&page=1"
        logging.info(f"🔍 Pobieranie pierwszej strony w celu określenia liczby stron: {first_page_url}")

        html_response = fetch_page(first_page_url)
        if html_response is None:
            raise Exception(f"Nie udało się pobrać pierwszej strony: {first_page_url}")

        soup = BeautifulSoup(html_response.text, 'html.parser')
        script_tag = soup.find('script', {'id': '__NEXT_DATA__'})

        if not script_tag:
            raise Exception(f"Brak skryptu __NEXT_DATA__ na pierwszej stronie: {first_page_url}")

        json_data = json.loads(script_tag.get_text())
        page_count = json_data.get("props", {}).get("pageProps", {}).get("data", {}).get("searchAds", {}).get("pagination", {}).get("totalPages", 1)

        logging.info(f"📊 Znaleziono {page_count} stron z ofertami wynajmu - przetwarzanie strona po stronie")

        # Ograniczenie dla testów - usuń tę linię w produkcji
        # page_count = 1

        # Przetwarzaj każdą stronę osobno
        for page in range(1, page_count + 1):
            page_url = f"{base_url}&page={page}"
            logging.info(f"📄 [{page}/{page_count}] Pobieranie strony: {page_url}")

            # Jeśli to pierwsza strona, użyj już pobranej odpowiedzi
            if page == 1:
                current_response = html_response
                current_soup = soup
                current_script_tag = script_tag
                current_json_data = json_data
            else:
                current_response = fetch_page(page_url)
                if current_response is None:
                    logging.error(f"❌ Nie udało się pobrać strony {page} - pomijam")
                    continue

                current_soup = BeautifulSoup(current_response.text, 'html.parser')
                current_script_tag = current_soup.find('script', {'id': '__NEXT_DATA__'})

                if not current_script_tag:
                    logging.error(f"❌ Błąd przy stronie {page} ({page_url}): brak skryptu __NEXT_DATA__")
                    continue

                try:
                    current_json_data = json.loads(current_script_tag.get_text())
                except json.JSONDecodeError as e:
                    logging.error(f"❌ Błąd parsowania JSON na stronie {page} ({page_url}): {e}")
                    continue

            # Wyodrębnij oferty z bieżącej strony
            offers = current_json_data.get("props", {}).get("pageProps", {}).get("data", {}).get("searchAds", {}).get("items", [])

            if not offers:
                logging.warning(f"⚠️ Brak ofert wynajmu na stronie {page}")
                continue

            page_offers = []
            n = 1

            for offer in offers:
                listing_id = offer.get("id", None)
                area = offer.get("areaInSquareMeters", None)

                # Dla wynajmu używamy totalPrice jako miesięczną cenę wynajmu
                total_price = offer.get("totalPrice", {})
                rental_price = total_price.get("value", None) if isinstance(total_price, dict) else None
                ppm_data = offer.get("pricePerSquareMeter", {})
                if ppm_data:
                    rental_price_per_m = ppm_data.get("value", None)
                else:
                    rental_price_per_m = None
                link = f"https://www.otodom.pl/pl/oferta/{offer.get('slug', None)}"

                development_id = offer.get("developmentId", None)

                # Sprawdź czy to nie jest oferta zbiorowa (development)
                if development_id is not None:
                    logging.debug(f"Oferta {listing_id} należy do inwestycji {development_id} - sprawdzanie powiązanych ofert")

                    # Sprawdź czy są powiązane oferty
                    related_offers = offer.get("relatedOffers", [])
                    if related_offers:
                        # Dodaj każdą powiązaną ofertę osobno
                        for related_offer in related_offers:
                            related_listing_id = related_offer.get("id", None)
                            related_area = related_offer.get("areaInSquareMeters", None)

                            related_total_price = related_offer.get("totalPrice", {})
                            related_rental_price = related_total_price.get("value", None) if isinstance(related_total_price, dict) else None

                            related_ppm_data = related_offer.get("pricePerSquareMeter", {})
                            if related_ppm_data:
                                related_rental_price_per_m = related_ppm_data.get("value", None)
                            else:
                                related_rental_price_per_m = None

                            related_link = f"https://www.otodom.pl/pl/oferta/{related_offer.get('slug', None)}"

                            page_offers.append({
                                'listing_id': related_listing_id,
                                'area': related_area,
                                'rental_price': related_rental_price,
                                'rental_price_per_m': related_rental_price_per_m,
                                'link': related_link
                            })

                            logging.debug(f"Strona {page}, oferta {n}: ID {related_listing_id}, powierzchnia {related_area}m2, cena wynajmu {related_rental_price}, cena za m2 {related_rental_price_per_m}")
                            n += 1
                    else:
                        # Jeśli brak powiązanych ofert, dodaj główną ofertę
                        page_offers.append({
                            'listing_id': listing_id,
                            'area': area,
                            'rental_price': rental_price,
                            'rental_price_per_m': rental_price_per_m,
                            'link': link
                        })

                        logging.debug(f"Strona {page}, oferta {n}: ID {listing_id}, powierzchnia {area}m2, cena wynajmu {rental_price}, cena za m2 {rental_price_per_m}")
                        n += 1
                else:
                    # Standardowa oferta bez development_id
                    page_offers.append({
                        'listing_id': listing_id,
                        'area': area,
                        'rental_price': rental_price,
                        'rental_price_per_m': rental_price_per_m,
                        'link': link
                    })

                    logging.debug(f"Strona {page}, oferta {n}: ID {listing_id}, powierzchnia {area}m2, cena wynajmu {rental_price}, cena za m2 {rental_price_per_m}")
                    n += 1

            logging.info(f"✅ [{page}/{page_count}] Znaleziono {len(page_offers)} ofert na stronie {page}")

            # Yield current page data for immediate processing
            yield page, page_count, page_offers

        logging.info(f"🎉 Zakończono pobieranie wszystkich {page_count} stron")

    except Exception as error:
        logging.exception(f"❌ Błąd podczas pobierania danych z wyników wyszukiwania wynajmu: {error}")
        raise


def download_rental_data_from_search_results(base_url: str) -> list:
    """
    Extracts rental listing information from all paginated search result pages on otodom.com.

    This function iterates through all pages of rental search results starting from the given base URL,
    parses the embedded JSON data in each page's HTML, and collects basic information about 
    each rental listing (ID, area, price, price_per_m and link)

    Args:
        base_url (str): The base search URL for rental apartments (without the `&page=` parameter)

    Returns:
        list: A list of dictionaries, each containing:
            - listing_id (int): listing ID from otodom or None
            - area (float): area of the apartment in m2 or 0
            - rental_price (int): Monthly rental price or None
            - rental_price_per_m (float): Rental price per m2 or None
            - link (str): URL to the individual listing or None
            - development_id (int): id of the specific investment to which the offer belongs or None

    Raises:
        Exception: If the first page fails to load, or if parsing fails due to a missing or incorrect script tag
        ValueError: If the script tag does not contain the expected data structure
    """
    try:
        all_offers = []
        
        # Pobierz pierwszą stronę, żeby sprawdzić ile jest stron w sumie
        first_page_url = f"{base_url}&page=1"
        logging.debug(f"Pobieranie pierwszej strony: {first_page_url}")
        
        html_response = fetch_page(first_page_url)
        if html_response is None:
            raise Exception(f"Nie udało się pobrać pierwszej strony: {first_page_url}")

        soup = BeautifulSoup(html_response.text, 'html.parser')
        script_tag = soup.find('script', {'id': '__NEXT_DATA__'})

        if not script_tag:
            raise Exception(f"Brak skryptu __NEXT_DATA__ na pierwszej stronie: {first_page_url}")

        json_data = json.loads(script_tag.get_text())
        page_count = json_data.get("props", {}).get("pageProps", {}).get("data", {}).get("searchAds", {}).get("pagination", {}).get("totalPages", 1)
        
        logging.info(f"Znaleziono {page_count} stron z ofertami wynajmu")        
        for page in range(1, page_count+1):
            page_url = f"{base_url}&page={page}"
            logging.debug(f"Pobieranie strony {page} z {page_count} ({page_url})")
            
            html_response = fetch_page(page_url)
            if html_response is None:
                logging.exception(f"Nie udało się pobrać strony {page}.")
                continue

            soup = BeautifulSoup(html_response.text, 'html.parser')
            script_tag = soup.find('script', {'id': '__NEXT_DATA__'})

            if not script_tag:
                logging.error(f"Błąd przy stronie {page} ({page_url}): brak skryptu __NEXT_DATA__.")
                logging.error(f"Fragment odpowiedzi strony (pierwsze 500 znaków): {html_response.text[:500]}")
                continue

            try:
                json_data = json.loads(script_tag.string)
            except json.JSONDecodeError as e:
                logging.error(f"Błąd parsowania JSON na stronie {page} ({page_url}): {e}")
                logging.error(f"Fragment skryptu (pierwsze 500 znaków): {script_tag.string[:500]}")
                continue

            offers = json_data.get("props", {}).get("pageProps", {}).get("data", {}).get("searchAds", {}).get("items", [])

            if not offers:
                logging.exception(f"Brak ofert wynajmu na stronie {page} url {base_url}")
                continue
            
            n=1
            for offer in offers: 
                # sprawdz czy nie jest to zbiorowe ogloszenie do ktorego nie mam obslugi

                listing_id = offer.get("id")
                area_raw = offer.get("areaInSquareMeters")
                area = 0.0
                
                if area_raw is not None:
                    try:
                        area = float(area_raw)
                    except (ValueError, TypeError):
                        logging.warning(f"Nie można przekonwertować powierzchni '{area_raw}' na float dla oferty {listing_id}")
                        area = 0.0
                else:
                    logging.warning(f"Brak danych o powierzchni dla oferty {listing_id}")
                    area = 0.0

                # Dla wynajmu używamy totalPrice jako miesięczną cenę wynajmu
                total_price = offer.get("totalPrice", {})
                rental_price = total_price.get("value", None) if isinstance(total_price, dict) else None
                ppm_data = offer.get("pricePerSquareMeter", {})
                if ppm_data:
                    rental_price_per_m = ppm_data.get("value", None)
                else: 
                    rental_price_per_m = None
                link = f"https://www.otodom.pl/pl/oferta/{offer.get('slug', None)}"

                development_id = offer.get("developmentId", None)

                # Sprawdź czy to nie jest oferta zbiorowa (development)
                if development_id is not None:
                    logging.debug(f"Oferta {listing_id} należy do inwestycji {development_id} - sprawdzanie powiązanych ofert")
                    
                    # Pobierz powiązane oferty z tej inwestycji
                    related_offers = offer.get("relatedOffers", [])
                    if related_offers:
                        for related_offer in related_offers:
                            related_listing_id = related_offer.get("id")
                            related_area_raw = related_offer.get("areaInSquareMeters")
                            related_area = 0.0
                            
                            if related_area_raw is not None:
                                try:
                                    related_area = float(related_area_raw)
                                except (ValueError, TypeError):
                                    related_area = 0.0
                            
                            related_total_price = related_offer.get("totalPrice", {})
                            related_rental_price = related_total_price.get("value", None) if isinstance(related_total_price, dict) else None
                            related_ppm_data = related_offer.get("pricePerSquareMeter", {})
                            if related_ppm_data:
                                related_rental_price_per_m = related_ppm_data.get("value", None)
                            else: 
                                related_rental_price_per_m = None
                            related_link = f"https://www.otodom.pl/pl/oferta/{related_offer.get('slug', None)}"

                            all_offers.append({
                                'listing_id': related_listing_id,
                                'area': related_area,
                                'rental_price': related_rental_price,
                                'rental_price_per_m': related_rental_price_per_m,
                                'link': related_link
                            })
                            
                            logging.debug(f"Strona {page}, oferta {n}: ID {related_listing_id}, powierzchnia {related_area}m2, cena wynajmu {related_rental_price}, cena za m2 {related_rental_price_per_m}")
                            n += 1
                    else:
                        # Jeśli brak powiązanych ofert, dodaj główną ofertę
                        all_offers.append({
                            'listing_id': listing_id,
                            'area': area,
                            'rental_price': rental_price,
                            'rental_price_per_m': rental_price_per_m,
                            'link': link
                        })
                        
                        logging.debug(f"Strona {page}, oferta {n}: ID {listing_id}, powierzchnia {area}m2, cena wynajmu {rental_price}, cena za m2 {rental_price_per_m}")
                        n += 1
                else:
                    # Standardowa oferta bez development_id
                    all_offers.append({
                        'listing_id': listing_id,
                        'area': area,
                        'rental_price': rental_price,
                        'rental_price_per_m': rental_price_per_m,
                        'link': link
                    })
                    
                    logging.debug(f"Strona {page}, oferta {n}: ID {listing_id}, powierzchnia {area}m2, cena wynajmu {rental_price}, cena za m2 {rental_price_per_m}")
                    n += 1

        logging.info(f"Pobrano łącznie {len(all_offers)} ofert wynajmu z {page_count} stron")
        return all_offers

    except Exception as e:
        logging.exception(f"Nieoczekiwany błąd podczas pobierania danych z wyników wyszukiwania wynajmu: {e}")
        return []


def check_if_rental_offer_exists(offer: dict, cur) -> bool:
    """
    Checks if a rental offer already exists in the database.

    Args:
        offer (dict): Dictionary containing offer data with 'listing_id' key
        cur: Database cursor to execute SQL queries

    Returns:
        bool: True if the offer exists in the database, False otherwise
    """
    try:
        listing_id = offer.get("listing_id")
        if listing_id is None:
            logging.warning("Brak listing_id w ofercie - nie można sprawdzić istnienia")
            return False

        query = """
            SELECT id FROM apartments_rental_listings
            WHERE otodom_listing_id = %s
            ;"""
        
        cur.execute(query, (listing_id,))
        result = cur.fetchone()
        
        exists = result is not None
        logging.debug(f"Oferta wynajmu {listing_id} {'istnieje' if exists else 'nie istnieje'} w bazie")
        
        return exists
        
    except Exception as error:
        logging.exception(f"Błąd podczas sprawdzania istnienia oferty wynajmu {offer.get('listing_id', 'UNKNOWN')}: {error}")
        return False


def check_if_rental_price_changed(offer: dict, cur) -> tuple | None:
    """
    Checks if the rental price of an existing offer has changed.

    Args:
        offer (dict): Dictionary containing offer data with 'listing_id' and 'rental_price' keys
        cur: Database cursor to execute SQL queries

    Returns:
        tuple | None: (id_db, new_price, new_price_per_m) if price changed or error occurred,
                     (id_db, False, False) if price hasn't changed,
                     None if offer doesn't exist or error occurred
    """
    try:
        listing_id = offer.get("listing_id")
        new_rental_price = offer.get("rental_price")
        new_rental_price_per_m = offer.get("rental_price_per_m")
        
        if listing_id is None:
            logging.warning("Brak listing_id w ofercie - nie można sprawdzić zmiany ceny")
            return None

        query = """
            SELECT id, updated_rental_price, updated_rental_price_per_m 
            FROM apartments_rental_listings
            WHERE otodom_listing_id = %s
            ;"""
        
        cur.execute(query, (listing_id,))
        result = cur.fetchone()
        
        if result is None:
            logging.warning(f"Oferta wynajmu {listing_id} nie istnieje w bazie")
            return None
            
        id_db, current_price, current_price_per_m = result
        
        # Sprawdź czy cena się zmieniła
        price_changed = (new_rental_price != current_price) or (new_rental_price_per_m != current_price_per_m)
        
        if price_changed:
            logging.debug(f"Cena wynajmu oferty {listing_id} zmieniła się: {current_price} -> {new_rental_price}")
            return (id_db, new_rental_price, new_rental_price_per_m)
        else:
            logging.debug(f"Cena wynajmu oferty {listing_id} nie zmieniła się: {current_price}")
            return (id_db, False, False)
            
    except Exception as error:
        logging.exception(f"Błąd podczas sprawdzania zmiany ceny wynajmu dla oferty {offer.get('listing_id', 'UNKNOWN')}: {error}")
        return None


def find_closed_rental_offers(data: list, city: str, cur) -> set:
    """
    Finds the rental offers that have been closed or removed

    Args:
        data (list): List of dictionaries containing rental offer data from Otodom, including 'listing_id', 
        'area', 'rental_price', 'rental_price_per_m', 'link' (all data from download_rental_data_from_search_results())
        city (str): City for which we are looking for rental apartments 
        cur (cursor): Database cursor to execute SQL queries

    Returns:
        set: A set of tuples containing (offer_id_from_db, offer_status) for closed rental offers
    """
    try:
        # Pobierz wszystkie aktywne oferty wynajmu z bazy dla danego miasta
        query = """
            SELECT arl.id, arl.otodom_listing_id 
            FROM apartments_rental_listings arl
            JOIN locations l ON arl.location_id = l.id
            WHERE l.city = %s AND arl.active = true
            ;"""
        
        cur.execute(query, (city,))
        db_offers = cur.fetchall()
        
        # Utwórz zbiór ID ofert z aktualnych wyników wyszukiwania
        current_listing_ids = {offer.get('listing_id') for offer in data if offer.get('listing_id') is not None}
        
        # Znajdź oferty, które są w bazie ale nie ma ich w aktualnych wynikach
        closed_offers = set()
        for db_id, otodom_id in db_offers:
            if otodom_id not in current_listing_ids:
                closed_offers.add((db_id, False))  # False oznacza nieaktywną ofertę
                logging.debug(f"Oferta wynajmu {otodom_id} (db_id: {db_id}) została usunięta z otodom")
        
        logging.info(f"Znaleziono {len(closed_offers)} usuniętych ofert wynajmu dla miasta {city}")
        return closed_offers
        
    except Exception as error:
        logging.exception(f"Błąd podczas wyszukiwania usuniętych ofert wynajmu dla miasta {city}: {error}")
        return set()


def download_rental_data_from_listing_page(html_response: requests.Response | None) -> dict | None:
    """
    Parses the HTML response for a rental listing, extracts the property data embedded in a JSON object
    within a <script> tag, and returns it as a dictionary with rental-specific fields.

    Parameters:
        html_response (requests.Response): The HTTP response containing the HTML of the rental listing page
        to be parsed.

    Returns:
        dict: A dictionary containing the extracted rental property listing data, such as title, rental_price,
        location, features, images, deposit_amount, etc.

    Raises:
        Exception: If the HTML response does not contain the necessary data or is invalid.
    """
    try:
        if html_response is None:
            logging.error("html_response is None in download_rental_data_from_listing_page.")
            return None

        soup = BeautifulSoup(html_response.text, 'html.parser')
        script_tag = soup.find('script', {'id': '__NEXT_DATA__'})

        if not script_tag:
            logging.error(f"Brak skryptu __NEXT_DATA__ na stronie oferty wynajmu: {html_response.url}")
            return None

        try:
            json_data = json.loads(script_tag.get_text())
        except json.JSONDecodeError as e:
            logging.error(f"Błąd parsowania JSON na stronie oferty wynajmu {html_response.url}: {e}")
            return None

        offer_data = json_data.get("props", {}).get("pageProps", {}).get("ad", {})

        if not offer_data:
            logging.error(f"Brak danych oferty w JSON na stronie: {html_response.url}")
            return None

        data = {}

        # Podstawowe informacje o ofercie wynajmu
        listing_id = offer_data.get("id", None)
        listing_title = offer_data.get("title", None)
        listing_title = BeautifulSoup(listing_title, "html.parser").get_text()
        market_type = str(offer_data.get("market", None)).lower()
        advertisement_type = str(offer_data.get("advertType", None)).lower()
        creation_date = offer_data.get("createdAt", None)
        description = offer_data.get("description", None)
        description_text = BeautifulSoup(description, "html.parser").get_text()
        is_exclusive_offer = offer_data.get("exclusiveOffer", None) # True/False
        creation_source = str(offer_data.get("creationSource", None))
        promoted_at = offer_data.get("pushedUpAt", None)

        # Cena wynajmu (główna różnica od sprzedaży)
        rental_price = offer_data.get("totalPrice", {}).get("value", None)
        rental_price_per_m = offer_data.get("pricePerSquareMeter", {}).get("value", None)

        # Dodatkowe opłaty specyficzne dla wynajmu
        deposit_amount = None
        additional_rent = None
        utilities_included = False

        # Sprawdź czy są dodatkowe opłaty w opisie lub charakterystykach
        characteristics = offer_data.get("characteristics", [])
        for char in characteristics:
            if char.get("key") == "deposit":
                deposit_amount = char.get("value")
            elif char.get("key") == "rent":
                additional_rent = char.get("value")
            elif char.get("key") == "utilities_included":
                utilities_included = char.get("value", False)

        # Powierzchnia - sprawdź różne możliwe lokalizacje w JSON
        area = offer_data.get("areaInSquareMeters", None)

        # Jeśli nie ma w standardowym miejscu, sprawdź target.Area
        if area is None:
            target_data = offer_data.get("target", {})
            if target_data:
                area = target_data.get("Area", None)
                logging.debug(f"Znaleziono powierzchnię w target.Area: {area}")

        # Jeśli nadal None, sprawdź w characteristics
        if area is None:
            characteristics = offer_data.get("characteristics", [])
            for char in characteristics:
                if isinstance(char, dict):
                    key = char.get("key", "").lower()
                    label = char.get("label", "").lower()
                    if "area" in key or "powierzchnia" in label or "metr" in label:
                        area = char.get("value", None)
                        logging.debug(f"Znaleziono powierzchnię w characteristics: {area} (key: {char.get('key')}, label: {char.get('label')})")
                        break

        # Lokalizacja
        location = offer_data.get("location", {})
        if location is None:
            location = {}

        # Extract location data with fallbacks
        province_data = location.get("province", {})
        voivodeship = province_data.get("name", None) if province_data else None

        city_data = location.get("city", {})
        city = city_data.get("name", None) if city_data else None

        district_data = location.get("district", {})
        district = district_data.get("name", None) if district_data else None

        address_data = location.get("address", {})
        street_data = address_data.get("street", {}) if address_data else {}
        street = street_data.get("name", None) if street_data else None

        # If location data is missing, try to extract from URL or other sources
        if not voivodeship or not city:
            # Try to extract from URL
            offer_url = f"https://www.otodom.pl/pl/oferta/{offer_data.get('slug', '')}"
            logging.debug(f"Trying to extract location from URL: {offer_url}")

            # Check if URL contains location info (common pattern in Otodom URLs)
            url_lower = offer_url.lower()
            if "warszawa" in url_lower or "warsaw" in url_lower:
                city = "warszawa"
                voivodeship = "mazowieckie"
            elif "kraków" in url_lower or "krakow" in url_lower:
                city = "kraków"
                voivodeship = "małopolskie"
            elif "gdańsk" in url_lower or "gdansk" in url_lower:
                city = "gdańsk"
                voivodeship = "pomorskie"
            elif "katowice" in url_lower:
                city = "katowice"
                voivodeship = "śląskie"
            elif "wrocław" in url_lower or "wroclaw" in url_lower:
                city = "wrocław"
                voivodeship = "dolnośląskie"
            elif "poznań" in url_lower or "poznan" in url_lower:
                city = "poznań"
                voivodeship = "wielkopolskie"
            elif "łódź" in url_lower or "lodz" in url_lower:
                city = "łódź"
                voivodeship = "łódzkie"

            # If still no location, check the title or description
            if not city:
                title = offer_data.get("title", "").lower()
                description = offer_data.get("description", "").lower()
                combined_text = f"{title} {description}"

                if "warszawa" in combined_text or "warsaw" in combined_text:
                    city = "warszawa"
                    voivodeship = "mazowieckie"
                elif "kraków" in combined_text or "krakow" in combined_text:
                    city = "kraków"
                    voivodeship = "małopolskie"
                # Add more cities as needed

        # If voivodeship is missing but city is available, infer it
        if not voivodeship and city:
            city_lower = city.lower()
            if city_lower == "warszawa":
                voivodeship = "mazowieckie"
            elif city_lower == "kraków":
                voivodeship = "małopolskie"
            elif city_lower == "gdańsk":
                voivodeship = "pomorskie"
            elif city_lower == "katowice":
                voivodeship = "śląskie"
            elif city_lower == "wrocław":
                voivodeship = "dolnośląskie"
            elif city_lower == "poznań":
                voivodeship = "wielkopolskie"
            elif city_lower == "łódź":
                voivodeship = "łódzkie"
            else:
                logging.warning(f"Could not infer voivodeship for city: {city}")

        # Final fallback - if we're scraping Warsaw URLs, assume Warsaw
        if not city and not voivodeship:
            logging.warning("No location data found, using fallback: Warszawa, Mazowieckie")
            city = "warszawa"
            voivodeship = "mazowieckie"

        logging.debug(f"Location extracted: voivodeship={voivodeship}, city={city}, district={district}, street={street}")

        # Szczegóły nieruchomości
        property_details = offer_data.get("property", {})
        if property_details is None:
            property_details = {}

        rooms_num = property_details.get("roomsNumber", None)
        floor_num = property_details.get("floor", None)

        # Szczegóły budynku
        building_properties = property_details.get("buildingProperties", {})
        if building_properties is None:
            building_properties = {}

        heating_type = str(building_properties.get("heating", None)).lower() if building_properties.get("heating") else None
        ownership = building_properties.get("ownership", None)
        construction_status = building_properties.get("constructionStatus", None)
        energy_certificate = building_properties.get("energyCertificate", None)
        building_build_year = building_properties.get("buildYear", None)
        building_floors_count = building_properties.get("floorsNumber", None)
        building_material = building_properties.get("buildingMaterial", None)
        building_type = building_properties.get("buildingType", None)
        windows_type = building_properties.get("windowsType", None)

        # Cechy i wyposażenie
        features_utilities = property_details.get("utilities", [])
        features_equipment = property_details.get("equipment", [])
        features_additional_information = property_details.get("additionalInformation", [])
        security_types = property_details.get("security", [])

        # Połącz wszystkie cechy w jeden string
        all_features = features_utilities + features_equipment + features_additional_information + security_types
        features_string = ' '.join(all_features) if all_features else ''

        # Linki do multimediów
        local_plan_url = offer_data.get("localPlanUrl", None)
        video_url = offer_data.get("videoUrl", None)
        view3d_url = offer_data.get("view3dUrl", None)
        walkaround_url = offer_data.get("walkaroundUrl", None)

        # Zdjęcia
        images_data = offer_data.get("images", [])
        images = []
        for img in images_data:
            if img.get("large"):
                images.append(img["large"])

        # Właściciel/Agent
        owner_data = offer_data.get("owner", {})
        owner_id = owner_data.get("id", None)
        owner_name = owner_data.get("name", None)

        agency_data = owner_data.get("agency", {})
        agency_id = agency_data.get("id", None) if agency_data else None
        agency_name = agency_data.get("name", None) if agency_data else None

        # Wypełnij słownik danych
        data["listing_id"] = listing_id
        data["title"] = listing_title
        data["market"] = market_type
        data["advert_type"] = advertisement_type
        data["creation_date"] = creation_date
        data["pushed_ap_at"] = promoted_at
        data["exclusive_offer"] = is_exclusive_offer
        data["creation_source"] = creation_source
        data["description_text"] = description_text
        data["area"] = area
        data["rental_price"] = rental_price
        data["rental_price_per_m"] = rental_price_per_m
        data["deposit_amount"] = deposit_amount
        data["utilities_included"] = utilities_included
        data["additional_rent"] = additional_rent
        data["voivodeship"] = voivodeship
        data["city"] = city
        data["district"] = district
        data["street"] = street
        data["rooms_num"] = rooms_num
        data["floor_num"] = floor_num
        data["heating"] = heating_type
        data["ownership"] = ownership
        data["proper_type"] = "mieszkanie"  # Typ nieruchomości dla wynajmu
        data["construction_status"] = construction_status
        data["energy_certificate"] = energy_certificate
        data["building_build_year"] = building_build_year
        data["building_floors_num"] = building_floors_count
        data["building_material"] = building_material
        data["building_type"] = building_type
        data["windows_type"] = windows_type
        data["features"] = features_string
        data["local_plan_url"] = local_plan_url
        data["video_url"] = video_url
        data["view3d_url"] = view3d_url
        data["walkaround_url"] = walkaround_url
        data["images"] = images
        data["owner_id"] = owner_id
        data["owner_name"] = owner_name
        data["agency_id"] = agency_id
        data["agency_name"] = agency_name
        data["offer_link"] = f"https://www.otodom.pl/pl/oferta/{offer_data.get('slug', '')}"
        data['active'] = True
        data['closing_date'] = None

        logging.debug(f"Pobrano dane oferty wynajmu: {data['offer_link']}, województwo: {voivodeship}")

        return data

    except Exception as e:
        logging.exception(f"Nieoczekiwany błąd podczas pobierania danych ze strony ogłoszenia wynajmu: {e}")
        url_info = f" (URL: {html_response.url})" if html_response and hasattr(html_response, 'url') else ""
        logging.error(f"Błąd dotyczył strony: {url_info}")
        return None
