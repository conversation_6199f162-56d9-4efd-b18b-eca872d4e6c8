#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to analyze what data is actually available in the JSON response
from rental listing pages that we can extract to fill empty fields.

This script:
1. Fetches a sample listing page
2. Analyzes the JSON structure
3. Maps available data to database fields
4. Shows what fields can be populated from the same response

Usage:
    python analyze_available_data.py
"""

import json
import logging
from scraper.rental_fetch_and_parse import fetch_page
from bs4 import BeautifulSoup
from config.logging_config import setup_logger

# Setup logging
logger = setup_logger()

def analyze_available_data(url):
    """
    Analyze what data is available in the JSON response.
    
    Args:
        url (str): The listing URL to analyze
    """
    
    print("="*100)
    print("ANALYZING AVAILABLE DATA IN RENTAL LISTING JSON")
    print("="*100)
    print(f"URL: {url}")
    print("="*100)
    
    try:
        # Fetch the page
        response = fetch_page(url)
        if response is None:
            print("❌ Failed to fetch page")
            return
        
        # Parse HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        script_tag = soup.find('script', {'id': '__NEXT_DATA__'})
        
        if not script_tag:
            print("❌ No __NEXT_DATA__ script tag found")
            return
        
        # Parse JSON
        try:
            json_data = json.loads(script_tag.get_text())
        except json.JSONDecodeError as e:
            print(f"❌ JSON parsing error: {e}")
            return
        
        # Extract offer data
        offer_data = json_data.get("props", {}).get("pageProps", {}).get("ad", {})
        
        if not offer_data:
            print("❌ No offer data found in JSON")
            return
        
        print("✅ Successfully extracted JSON data")
        
        # Map database fields to potential JSON paths
        field_mappings = {
            # Basic info
            'area': [
                'areaInSquareMeters',
                'target.Area',
                'property.areaInSquareMeters'
            ],
            'rental_price': [
                'totalPrice.value',
                'price.value',
                'target.Price'
            ],
            'rental_price_per_m': [
                'pricePerSquareMeter.value',
                'target.PricePerMeter'
            ],
            'rooms_num': [
                'property.roomsNumber',
                'target.RoomsNumber',
                'roomsNumber'
            ],
            'floor_num': [
                'property.floor',
                'target.Floor',
                'floor'
            ],
            
            # Building details
            'heating': [
                'property.buildingProperties.heating',
                'buildingProperties.heating'
            ],
            'ownership': [
                'property.buildingProperties.ownership',
                'buildingProperties.ownership'
            ],
            'construction_status': [
                'property.buildingProperties.constructionStatus',
                'buildingProperties.constructionStatus'
            ],
            'energy_certificate': [
                'property.buildingProperties.energyCertificate',
                'buildingProperties.energyCertificate'
            ],
            'building_build_year': [
                'property.buildingProperties.buildYear',
                'buildingProperties.buildYear'
            ],
            'building_floors_num': [
                'property.buildingProperties.floorsNumber',
                'buildingProperties.floorsNumber'
            ],
            'building_material': [
                'property.buildingProperties.buildingMaterial',
                'buildingProperties.buildingMaterial'
            ],
            'building_type': [
                'property.buildingProperties.buildingType',
                'buildingProperties.buildingType'
            ],
            'windows_type': [
                'property.buildingProperties.windowsType',
                'buildingProperties.windowsType'
            ],
            
            # Additional fields
            'deposit_amount': [
                'depositAmount',
                'deposit.value'
            ],
            'additional_rent': [
                'additionalRent',
                'rent.value'
            ],
            'agency_name': [
                'owner.agency.name',
                'agency.name'
            ]
        }
        
        def get_nested_value(obj, path):
            """Get value from nested object using dot notation"""
            keys = path.split('.')
            current = obj
            for key in keys:
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    return None
            return current
        
        print("\n" + "="*100)
        print("FIELD MAPPING ANALYSIS")
        print("="*100)
        
        available_fields = []
        missing_fields = []
        
        for db_field, json_paths in field_mappings.items():
            found_value = None
            found_path = None
            
            for path in json_paths:
                value = get_nested_value(offer_data, path)
                if value is not None:
                    found_value = value
                    found_path = path
                    break
            
            if found_value is not None:
                available_fields.append((db_field, found_path, found_value))
                print(f"✅ {db_field:<25} -> {found_path:<40} = {found_value}")
            else:
                missing_fields.append(db_field)
                print(f"❌ {db_field:<25} -> NOT FOUND")
        
        # Check characteristics array for additional data
        print("\n" + "="*100)
        print("CHARACTERISTICS ARRAY ANALYSIS")
        print("="*100)
        
        characteristics = offer_data.get("characteristics", [])
        if characteristics:
            print(f"Found {len(characteristics)} characteristics:")
            
            char_mappings = {}
            for i, char in enumerate(characteristics):
                if isinstance(char, dict):
                    key = char.get("key", "")
                    value = char.get("value", "")
                    label = char.get("label", "")
                    
                    print(f"  [{i:2d}] Key: {key:<30} Value: {str(value):<20} Label: {label}")
                    
                    # Try to map to database fields
                    key_lower = key.lower()
                    label_lower = label.lower() if label else ""
                    
                    if "room" in key_lower or "pokój" in label_lower:
                        char_mappings["rooms_num"] = value
                    elif "floor" in key_lower or "piętro" in label_lower:
                        char_mappings["floor_num"] = value
                    elif "heating" in key_lower or "ogrzewanie" in label_lower:
                        char_mappings["heating"] = value
                    elif "ownership" in key_lower or "własność" in label_lower:
                        char_mappings["ownership"] = value
                    elif "year" in key_lower or "rok" in label_lower:
                        char_mappings["building_build_year"] = value
                    elif "material" in key_lower or "materiał" in label_lower:
                        char_mappings["building_material"] = value
                    elif "window" in key_lower or "okna" in label_lower:
                        char_mappings["windows_type"] = value
            
            if char_mappings:
                print(f"\nMapped from characteristics:")
                for field, value in char_mappings.items():
                    print(f"  ✅ {field} = {value}")
        
        # Check target object for additional data
        print("\n" + "="*100)
        print("TARGET OBJECT ANALYSIS")
        print("="*100)
        
        target = offer_data.get("target", {})
        if target:
            print("Target object contents:")
            for key, value in target.items():
                print(f"  {key}: {value}")
        
        # Summary
        print("\n" + "="*100)
        print("SUMMARY")
        print("="*100)
        print(f"✅ Available fields: {len(available_fields)}")
        print(f"❌ Missing fields: {len(missing_fields)}")
        print(f"📊 Coverage: {len(available_fields)}/{len(field_mappings)} ({len(available_fields)/len(field_mappings)*100:.1f}%)")
        
        if available_fields:
            print(f"\nFields that CAN be extracted:")
            for field, path, value in available_fields:
                print(f"  • {field}")
        
        if missing_fields:
            print(f"\nFields that CANNOT be extracted:")
            for field in missing_fields:
                print(f"  • {field}")
        
        # Save detailed JSON for manual inspection
        with open('detailed_offer_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(offer_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ Full offer data saved to 'detailed_offer_analysis.json'")
        
    except Exception as error:
        logging.exception(f"❌ Error during analysis: {error}")


def main():
    """Main function"""
    
    # Use a sample URL
    sample_url = "https://www.otodom.pl/pl/oferta/mieszkanie-53-m2-dwa-pokoje-ul-bobrowiecka-3a-bezposrednio-ID4vJsR"
    
    analyze_available_data(sample_url)


if __name__ == "__main__":
    main()
