#!/usr/bin/env python3
"""
Enhanced script to reprocess rental listings and extract ALL available fields.

This script:
1. Finds all active rental listings with missing data
2. Re-fetches the listing page for each one
3. Extracts ALL available information from the JSON response
4. Updates the database with comprehensive data

Usage:
    python reprocess_all_fields.py
    python reprocess_all_fields.py --limit 100  # Process only first 100 listings
"""

import logging
import time
import random
import sys
from datetime import datetime

from db.db_setup import get_db_connection
from db.rental_db_operations import get_listings_with_null_area, update_listing_comprehensive
from scraper.rental_fetch_and_parse import fetch_page, download_rental_data_from_listing_page
from scraper.rental_transform_data import transform_rental_data
from config.logging_config import setup_logger

# Setup logging
logger = setup_logger()


def get_listings_with_missing_data(cur, limit=None):
    """Get all active rental listings that have missing critical data"""
    try:
        limit_clause = f"LIMIT {limit}" if limit else ""
        
        query = f"""
            SELECT id, otodom_listing_id, offer_link, area, rental_price, rooms_num, 
                   floor_num, heating, building_build_year
            FROM apartments_rental_listings
            WHERE active = true 
            AND (area IS NULL OR rental_price IS NULL OR rooms_num IS NULL 
                 OR floor_num IS NULL OR heating IS NULL OR building_build_year IS NULL)
            ORDER BY id
            {limit_clause}
            ;"""
        
        cur.execute(query)
        results = cur.fetchall()
        
        logging.info(f"Znaleziono {len(results)} aktywnych ofert z brakującymi danymi")
        return results
        
    except Exception as error:
        logging.exception(f"Błąd podczas pobierania ofert z brakującymi danymi: {error}")
        return []


def reprocess_single_listing_comprehensive(listing_info, conn, cur):
    """
    Reprocess a single listing to extract ALL available information.
    
    Args:
        listing_info (tuple): (id, otodom_listing_id, offer_link, area, rental_price, rooms_num, floor_num, heating, building_build_year)
        conn: Database connection
        cur: Database cursor
        
    Returns:
        dict: Results with success status and extracted fields count
    """
    db_id, otodom_listing_id, offer_link = listing_info[0], listing_info[1], listing_info[2]
    current_data = {
        'area': listing_info[3],
        'rental_price': listing_info[4], 
        'rooms_num': listing_info[5],
        'floor_num': listing_info[6],
        'heating': listing_info[7],
        'building_build_year': listing_info[8]
    }
    
    try:
        logging.info(f"🔄 Przetwarzam ofertę ID {db_id} (Otodom: {otodom_listing_id})")
        logging.debug(f"   Link: {offer_link}")
        
        # Count current missing fields
        missing_before = sum(1 for v in current_data.values() if v is None)
        logging.debug(f"   Brakujące pola przed: {missing_before}/6")
        
        # Fetch the listing page
        response = fetch_page(offer_link)
        if response is None:
            logging.error(f"❌ Nie udało się pobrać strony dla oferty ID {db_id}")
            return {'success': False, 'error': 'fetch_failed', 'extracted_fields': 0}
        
        # Extract data from the page
        offer_data = download_rental_data_from_listing_page(response)
        if offer_data is None:
            logging.error(f"❌ Nie udało się wyodrębnić danych z oferty ID {db_id}")
            return {'success': False, 'error': 'extraction_failed', 'extracted_fields': 0}
        
        # Transform the data to ensure proper formatting
        transformed_data = transform_rental_data(offer_data)
        if transformed_data is None:
            logging.error(f"❌ Nie udało się przetworzyć danych dla oferty ID {db_id}")
            return {'success': False, 'error': 'transform_failed', 'extracted_fields': 0}
        
        # Prepare data for update - only include fields that have values
        update_data = {}
        field_names = [
            'area', 'rental_price', 'rental_price_per_m', 'rooms_num', 'floor_num',
            'heating', 'ownership', 'construction_status', 'energy_certificate',
            'building_build_year', 'building_floors_num', 'building_material',
            'building_type', 'windows_type', 'deposit_amount', 'additional_rent', 'features'
        ]
        
        for field in field_names:
            value = transformed_data.get(field)
            # Special handling for different field types
            if value is not None:
                # For string fields, exclude empty strings
                if isinstance(value, str) and value.strip() == '':
                    continue
                # For numeric fields, allow 0 (e.g., ground floor = 0)
                elif isinstance(value, (int, float)) and field in ['floor_num', 'rooms_num', 'building_floors_num']:
                    update_data[field] = value
                # For other numeric fields, exclude 0
                elif isinstance(value, (int, float)) and value == 0:
                    continue
                else:
                    update_data[field] = value
        
        if not update_data:
            logging.warning(f"⚠️ Brak nowych danych do aktualizacji dla oferty ID {db_id}")
            return {'success': False, 'error': 'no_data', 'extracted_fields': 0}
        
        # Update the database
        success = update_listing_comprehensive(db_id, update_data, conn, cur)
        if success:
            extracted_count = len(update_data)
            logging.info(f"✅ Oferta ID {db_id} zaktualizowana: {extracted_count} pól")
            
            # Log some key extracted values
            key_fields = ['area', 'rental_price', 'rooms_num', 'floor_num', 'heating', 'building_build_year']
            extracted_summary = []
            for field in key_fields:
                if field in update_data:
                    extracted_summary.append(f"{field}={update_data[field]}")
            
            if extracted_summary:
                logging.info(f"   📊 Kluczowe dane: {', '.join(extracted_summary)}")
            
            return {'success': True, 'error': None, 'extracted_fields': extracted_count}
        else:
            logging.error(f"❌ Nie udało się zaktualizować oferty ID {db_id}")
            return {'success': False, 'error': 'update_failed', 'extracted_fields': 0}
            
    except Exception as error:
        logging.exception(f"❌ Nieoczekiwany błąd podczas przetwarzania oferty ID {db_id}: {error}")
        return {'success': False, 'error': 'unexpected_error', 'extracted_fields': 0}


def main():
    """Main function to reprocess listings with comprehensive data extraction"""
    
    # Parse command line arguments
    limit = None
    if '--limit' in sys.argv:
        try:
            limit_index = sys.argv.index('--limit')
            limit = int(sys.argv[limit_index + 1])
            print(f"🔢 Ograniczenie: przetwarzanie maksymalnie {limit} ofert")
        except (IndexError, ValueError):
            print("❌ Błędny format parametru --limit. Użyj: --limit 100")
            return
    
    print("="*80)
    print("COMPREHENSIVE REPROCESSING OF RENTAL LISTINGS")
    print("="*80)
    
    start_time = datetime.now()
    logging.info("🚀 Rozpoczynam kompleksowy reprocessing ofert wynajmu...")
    
    # Connect to database
    conn = get_db_connection()
    if not conn:
        logging.error("❌ Nie udało się połączyć z bazą danych")
        return
    
    cur = conn.cursor()
    
    try:
        # Get all listings with missing data
        logging.info("🔍 Wyszukuję oferty z brakującymi danymi...")
        listings_with_missing_data = get_listings_with_missing_data(cur, limit)
        
        if not listings_with_missing_data:
            logging.info("✅ Brak ofert z brakującymi danymi - wszystko w porządku!")
            return
        
        total_listings = len(listings_with_missing_data)
        logging.info(f"📊 Znaleziono {total_listings} ofert do przetworzenia")
        
        # Process each listing
        successful_updates = 0
        failed_updates = 0
        total_fields_extracted = 0
        
        for i, listing_info in enumerate(listings_with_missing_data, 1):
            logging.info(f"\n📋 [{i}/{total_listings}] Przetwarzam ofertę...")
            
            result = reprocess_single_listing_comprehensive(listing_info, conn, cur)
            
            if result['success']:
                successful_updates += 1
                total_fields_extracted += result['extracted_fields']
            else:
                failed_updates += 1
                logging.error(f"   ❌ Błąd: {result['error']}")
            
            # Add delay to avoid overwhelming the server
            if i < total_listings:  # Don't delay after the last item
                delay = random.uniform(1.5, 3.0)
                logging.debug(f"⏳ Czekam {delay:.1f}s przed następną ofertą...")
                time.sleep(delay)
        
        # Summary
        end_time = datetime.now()
        duration = end_time - start_time
        avg_fields_per_listing = total_fields_extracted / successful_updates if successful_updates > 0 else 0
        
        print("\n" + "="*80)
        print("PODSUMOWANIE KOMPLEKSOWEGO REPROCESSINGU")
        print("="*80)
        print(f"📊 Łącznie przetworzono:        {total_listings}")
        print(f"✅ Pomyślnie zaktualizowano:    {successful_updates}")
        print(f"❌ Błędy:                       {failed_updates}")
        print(f"📈 Łącznie wyodrębnionych pól:  {total_fields_extracted}")
        print(f"📊 Średnio pól na ofertę:       {avg_fields_per_listing:.1f}")
        print(f"⏱️ Czas wykonania:              {duration}")
        print("="*80)
        
        logging.info(f"🎉 Kompleksowy reprocessing zakończony!")
        logging.info(f"📊 Statystyki: {successful_updates} sukces, {failed_updates} błędów z {total_listings} łącznie")
        logging.info(f"📈 Wyodrębniono łącznie {total_fields_extracted} pól danych")
        logging.info(f"⏱️ Czas wykonania: {duration}")
        
    except Exception as error:
        logging.exception(f"❌ Nieoczekiwany błąd podczas reprocessingu: {error}")
    
    finally:
        # Close database connection
        if cur:
            cur.close()
        if conn:
            conn.close()
        logging.info("🔌 Połączenie z bazą danych zamknięte")


if __name__ == "__main__":
    main()
