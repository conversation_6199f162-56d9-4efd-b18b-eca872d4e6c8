#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to check the current status of rental listings with NULL area values.

This script:
1. Connects to the database
2. Counts the total number of rental listings
3. Counts the number of listings with NULL area values
4. Displays a summary of the findings

Usage:
    python check_null_areas.py
"""

import logging
from db.db_setup import get_db_connection
from config.logging_config import setup_logger

# Setup logging
logger = setup_logger()

def check_null_areas():
    """Check for rental listings with NULL area values"""
    
    print("="*60)
    print("CHECKING RENTAL LISTINGS WITH NULL AREA VALUES")
    print("="*60)
    
    # Connect to database
    conn = get_db_connection()
    if not conn:
        logging.error("❌ Nie udało się połączyć z bazą danych")
        return
    
    cur = conn.cursor()
    
    try:
        # Count total listings
        cur.execute("""
            SELECT COUNT(*) FROM apartments_rental_listings
        """)
        total_listings = cur.fetchone()[0]
        
        # Count active listings
        cur.execute("""
            SELECT COUNT(*) FROM apartments_rental_listings
            WHERE active = true
        """)
        active_listings = cur.fetchone()[0]
        
        # Count listings with NULL area
        cur.execute("""
            SELECT COUNT(*) FROM apartments_rental_listings
            WHERE area IS NULL
        """)
        null_area_listings = cur.fetchone()[0]
        
        # Count active listings with NULL area
        cur.execute("""
            SELECT COUNT(*) FROM apartments_rental_listings
            WHERE area IS NULL AND active = true
        """)
        active_null_area_listings = cur.fetchone()[0]
        
        # Get sample of listings with NULL area
        cur.execute("""
            SELECT id, otodom_listing_id, offer_link, active
            FROM apartments_rental_listings
            WHERE area IS NULL
            ORDER BY id
            LIMIT 10
        """)
        sample_listings = cur.fetchall()
        
        # Display results
        print(f"📊 Łączna liczba ofert wynajmu:           {total_listings}")
        print(f"📊 Liczba aktywnych ofert wynajmu:        {active_listings}")
        print(f"📊 Liczba ofert z brakującą powierzchnią: {null_area_listings}")
        print(f"📊 Aktywne oferty z brakującą powierzchnią: {active_null_area_listings}")
        
        if null_area_listings > 0:
            print("\n📋 Przykładowe oferty z brakującą powierzchnią:")
            print("-"*60)
            print(f"{'ID':^6} | {'Otodom ID':^10} | {'Aktywna':^7} | {'Link':^40}")
            print("-"*60)
            
            for listing in sample_listings:
                db_id, otodom_id, link, is_active = listing
                status = "✅" if is_active else "❌"
                print(f"{db_id:^6} | {otodom_id:^10} | {status:^7} | {link[:40]}...")
        
        print("="*60)
        
        # Log results
        logging.info(f"📊 Łączna liczba ofert wynajmu: {total_listings}")
        logging.info(f"📊 Liczba aktywnych ofert wynajmu: {active_listings}")
        logging.info(f"📊 Liczba ofert z brakującą powierzchnią: {null_area_listings}")
        logging.info(f"📊 Aktywne oferty z brakującą powierzchnią: {active_null_area_listings}")
        
    except Exception as error:
        logging.exception(f"❌ Nieoczekiwany błąd podczas sprawdzania ofert: {error}")
    
    finally:
        # Close database connection
        if cur:
            cur.close()
        if conn:
            conn.close()
        logging.info("🔌 Połączenie z bazą danych zamknięte")

if __name__ == "__main__":
    check_null_areas()
