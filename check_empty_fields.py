#!/usr/bin/env python3
"""
Script to check which fields are commonly NULL/empty in rental listings.

This script:
1. Connects to the database
2. Analyzes all rental listing fields
3. Reports which fields have high NULL/empty rates
4. Helps identify what other data we can potentially extract

Usage:
    python check_empty_fields.py
"""

import logging
from db.db_setup import get_db_connection
from config.logging_config import setup_logger

# Setup logging
logger = setup_logger()

def analyze_empty_fields():
    """Analyze which fields are commonly empty in rental listings"""
    
    print("="*80)
    print("ANALYZING EMPTY FIELDS IN RENTAL LISTINGS")
    print("="*80)
    
    # Connect to database
    conn = get_db_connection()
    if not conn:
        logging.error("❌ Nie udało się połączyć z bazą danych")
        return
    
    cur = conn.cursor()
    
    try:
        # Get total count
        cur.execute("SELECT COUNT(*) FROM apartments_rental_listings")
        total_count = cur.fetchone()[0]
        
        print(f"📊 Łączna liczba ofert wynajmu: {total_count}")
        print("\n" + "="*80)
        print("ANALIZA PUSTYCH PÓL")
        print("="*80)
        
        # List of fields to check
        fields_to_check = [
            'area',
            'rental_price',
            'rental_price_per_m',
            'deposit_amount',
            'additional_rent',
            'rooms_num',
            'floor_num',
            'heating',
            'ownership',
            'construction_status',
            'energy_certificate',
            'building_build_year',
            'building_floors_num',
            'building_material',
            'building_type',
            'windows_type',
            'street',
            'description_text',
            'title',
            'owner_name',
            'agency_name'
        ]
        
        results = []
        
        # Define field types to handle different data types properly
        numeric_fields = ['area', 'rental_price', 'rental_price_per_m', 'deposit_amount', 'additional_rent',
                         'rooms_num', 'building_build_year', 'building_floors_num']
        text_fields = ['heating', 'ownership', 'construction_status', 'energy_certificate', 'building_material',
                      'building_type', 'windows_type', 'street', 'description_text', 'title', 'owner_name', 'agency_name']
        varchar_fields = ['floor_num']

        for field in fields_to_check:
            # Count NULL values
            cur.execute(f"""
                SELECT COUNT(*) FROM apartments_rental_listings
                WHERE {field} IS NULL
            """)
            null_count = cur.fetchone()[0]

            # Count empty/zero values based on field type
            empty_count = 0
            if field in text_fields:
                cur.execute(f"""
                    SELECT COUNT(*) FROM apartments_rental_listings
                    WHERE {field} = '' OR TRIM({field}) = ''
                """)
                empty_count = cur.fetchone()[0]
            elif field in numeric_fields:
                cur.execute(f"""
                    SELECT COUNT(*) FROM apartments_rental_listings
                    WHERE {field} = 0
                """)
                empty_count = cur.fetchone()[0]
            elif field in varchar_fields:
                cur.execute(f"""
                    SELECT COUNT(*) FROM apartments_rental_listings
                    WHERE {field} = '' OR {field} = '0'
                """)
                empty_count = cur.fetchone()[0]

            total_empty = null_count + empty_count
            percentage = (total_empty / total_count) * 100 if total_count > 0 else 0

            results.append((field, null_count, empty_count, total_empty, percentage))
        
        # Sort by percentage of empty values (highest first)
        results.sort(key=lambda x: x[4], reverse=True)
        
        print(f"{'Field':<25} | {'NULL':<6} | {'Empty':<6} | {'Total':<6} | {'%':<6}")
        print("-" * 80)
        
        high_priority_fields = []
        
        for field, null_count, empty_count, total_empty, percentage in results:
            status = ""
            if percentage > 80:
                status = "🔴 CRITICAL"
                high_priority_fields.append(field)
            elif percentage > 50:
                status = "🟡 HIGH"
                high_priority_fields.append(field)
            elif percentage > 20:
                status = "🟠 MEDIUM"
            elif percentage > 5:
                status = "🟢 LOW"
            else:
                status = "✅ GOOD"
            
            print(f"{field:<25} | {null_count:<6} | {empty_count:<6} | {total_empty:<6} | {percentage:>5.1f}% {status}")
        
        print("\n" + "="*80)
        print("HIGH PRIORITY FIELDS FOR REPROCESSING")
        print("="*80)
        
        if high_priority_fields:
            print("Fields with >50% empty values that should be prioritized:")
            for field in high_priority_fields:
                print(f"  • {field}")
        else:
            print("✅ No fields with critically high empty rates found")
        
        # Sample some records to see what data is available
        print("\n" + "="*80)
        print("SAMPLE DATA (first 5 records)")
        print("="*80)
        
        cur.execute("""
            SELECT id, otodom_listing_id, title, area, rental_price, rooms_num, 
                   floor_num, heating, building_build_year, street
            FROM apartments_rental_listings 
            ORDER BY id 
            LIMIT 5
        """)
        
        sample_data = cur.fetchall()
        
        print(f"{'ID':<4} | {'Otodom':<10} | {'Area':<6} | {'Price':<8} | {'Rooms':<6} | {'Floor':<6} | {'Heating':<12} | {'Year':<6}")
        print("-" * 80)
        
        for row in sample_data:
            id_val, otodom_id, title, area, price, rooms, floor, heating, year, street = row
            title_short = (title[:20] + "...") if title and len(title) > 20 else (title or "NULL")
            heating_short = (heating[:10] + "..") if heating and len(heating) > 10 else (heating or "NULL")
            
            print(f"{id_val:<4} | {otodom_id:<10} | {area or 'NULL':<6} | {price or 'NULL':<8} | {rooms or 'NULL':<6} | {floor or 'NULL':<6} | {heating_short:<12} | {year or 'NULL':<6}")
        
        print("="*80)
        
    except Exception as error:
        logging.exception(f"❌ Błąd podczas analizy pustych pól: {error}")
    
    finally:
        # Close database connection
        if cur:
            cur.close()
        if conn:
            conn.close()
        logging.info("🔌 Połączenie z bazą danych zamknięte")

if __name__ == "__main__":
    analyze_empty_fields()
