from db.db_setup import get_db_connection
import datetime, logging

def check_location_table(cur, offer_data):
    """Check if location exists in locations table - reuse from original"""
    location_query = """
        SELECT id FROM locations
        WHERE voivodeship = %s 
        AND city= %s 
        AND (district = %s OR (district IS NULL AND %s IS NULL))
        ;"""
    location_values = (offer_data['voivodeship'], 
                       offer_data['city'], 
                       offer_data['district'],
                       offer_data['district'])

    cur.execute(location_query, location_values)
    location_result = cur.fetchone()

    return location_result


def insert_into_locations_table(cur, offer_data):
    """Insert location if it doesn't exist - reuse from original"""
    # sprawdzenie czy lokalizacja już istnieje w bazie danych
    location_result = check_location_table(cur, offer_data)
    # jezeli nie istnieje to dodajemy do tabeli
    if not location_result:
        location_values = (offer_data['voivodeship'], 
                           offer_data['city'], 
                           offer_data['district'])
        

        logging.debug(f"Czy lokalizacja {location_values} znajduje się już w locations?: NIE")

        location_query = """
            INSERT INTO locations (voivodeship, city, district)
            VALUES (%s, %s, %s)
            RETURNING id
            ;"""
        
        cur.execute(location_query, location_values)
        new_id = cur.fetchone()[0]
        logging.debug(f"Nadane ID lokalizacji w tabeli locations: {new_id}")
        
    else:
        logging.debug(f"Czy lokalizacja znajduje się już w locations?: TAK, pod id {location_result}")


def insert_into_apartments_rental_listings_table(cur, offer_data):
    """Insert rental listing into apartments_rental_listings table"""
    location_id = check_location_table(cur, offer_data)
    listing_query = """
        INSERT INTO apartments_rental_listings (otodom_listing_id, title, market, advert_type, 
        creation_date, creation_time, pushed_ap_at, exclusive_offer, creation_source, description_text, 
        area, rental_price, updated_rental_price, rental_price_per_m, updated_rental_price_per_m, 
        deposit_amount, utilities_included, location_id, street, additional_rent, 
        rooms_num, floor_num, heating, ownership, proper_type, construction_status, energy_certificate, 
        building_build_year, building_floors_num,  building_material, building_type, windows_type,  
        local_plan_url, video_url, view3d_url, walkaround_url, owner_id, owner_name, agency_id, 
        agency_name, offer_link, active, closing_date)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        RETURNING id
        ;"""
    
    listing_values = (offer_data['listing_id'],
                      offer_data['title'],
                      offer_data['market'],
                      offer_data['advert_type'],
                      offer_data['creation_date'],
                      offer_data['creation_time'],
                      offer_data['pushed_ap_at'],
                      offer_data['exclusive_offer'],
                      offer_data['creation_source'],
                      offer_data['description_text'],
                      offer_data['area'],
                      offer_data['rental_price'],
                      offer_data['rental_price'], # przy pierwszym wprowadzeniu podajemy tą samą cene
                      offer_data['rental_price_per_m'],
                      offer_data['rental_price_per_m'], # przy pierwszym wprowadzeniu podajemy tą samą cene
                      offer_data.get('deposit_amount'),
                      offer_data.get('utilities_included', False),
                      location_id[0],
                      offer_data['street'],
                      offer_data.get('additional_rent'),
                      offer_data['rooms_num'],
                      offer_data['floor_num'],
                      offer_data['heating'],
                      offer_data['ownership'],
                      offer_data['proper_type'],
                      offer_data['construction_status'],
                      offer_data['energy_certificate'],
                      offer_data['building_build_year'],
                      offer_data['building_floors_num'],
                      offer_data['building_material'],
                      offer_data['building_type'],
                      offer_data['windows_type'],
                      offer_data['local_plan_url'],
                      offer_data['video_url'],
                      offer_data['view3d_url'],
                      offer_data['walkaround_url'],
                      offer_data['owner_id'],
                      offer_data['owner_name'],
                      offer_data['agency_id'],
                      offer_data['agency_name'],
                      offer_data['offer_link'],
                      offer_data['active'],
                      offer_data['closing_date'])
    
    cur.execute(listing_query, listing_values)
    
    created_offer_id = cur.fetchone()[0]

    return created_offer_id


def insert_into_rental_features_table(cur, offer_data, id):
    """Insert features for rental listing"""
    features_query = """
        INSERT INTO rental_features (listing_id, internet, cable_television, phone, roller_shutters, 
        anti_burglary_door, entryphone, monitoring, alarm, closed_area, furniture, washing_machine, 
        dishwasher, fridge, stove, oven, tv, balcony, usable_room, garage, basement, garden, terrace, 
        lift, two_storey, separate_kitchen, air_conditioning)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 
        %s, %s, %s, %s, %s, %s)
        ;"""

    features_offer = list(offer_data['features'].split(' '))
    features_all_possibilities = ('internet', 'cable_television', 'phone', 'roller_shutters', 
                                  'anti_burglary_door', 'entryphone', 'monitoring', 'alarm', 
                                  'closed_area', 'furniture', 'washing_machine', 'dishwasher', 
                                  'fridge', 'stove', 'oven', 'tv', 'balcony', 'usable_room', 
                                  'garage', 'basement', 'garden', 'terrace', 'lift', 'two_storey', 
                                  'separate_kitchen', 'air_conditioning')
    
    features_bools=[feature in features_offer for feature in features_all_possibilities]
    
    features_values = (id, *features_bools)
    cur.execute(features_query, features_values)


def insert_into_rental_photos_table(cur, offer_data, id):
    """Insert photos for rental listing"""
    if offer_data["images"]:
        photos_query = """
            INSERT INTO rental_photos (listing_id, photo)
            VALUES (%s, %s)
            ;"""    
        
        for photo in offer_data["images"]:
            photo_values = (id, photo)
            cur.execute(photos_query, photo_values)


def insert_new_rental_listing(offer_data, conn, cur):
    """Insert new rental listing with all related data - SAVES IMMEDIATELY TO DATABASE"""
    try:
        # Validate required data
        if not offer_data.get('voivodeship') or not offer_data.get('city'):
            logging.error(f"Missing location data: voivodeship={offer_data.get('voivodeship')}, city={offer_data.get('city')}")
            return None

        listing_id = offer_data.get('listing_id', 'Unknown')
        logging.debug(f"Rozpoczynam zapis oferty {listing_id} do bazy danych...")

        # TABELA locations
        logging.debug(f"Zapisuję lokalizację dla oferty {listing_id}...")
        insert_into_locations_table(cur, offer_data)

        # TABELA apartments_rental_listings
        logging.debug(f"Zapisuję główne dane oferty {listing_id}...")
        created_offer_id = insert_into_apartments_rental_listings_table(cur, offer_data)

        # TABELA rental_features
        logging.debug(f"Zapisuję cechy oferty {listing_id} (ID: {created_offer_id})...")
        insert_into_rental_features_table(cur, offer_data, created_offer_id)

        # TABELA rental_photos
        photo_count = len(offer_data.get("images", []))
        logging.debug(f"Zapisuję {photo_count} zdjęć dla oferty {listing_id} (ID: {created_offer_id})...")
        insert_into_rental_photos_table(cur, offer_data, created_offer_id)

        # NATYCHMIASTOWY COMMIT DO BAZY DANYCH
        logging.debug(f"Wykonuję COMMIT dla oferty {listing_id} (ID: {created_offer_id})...")
        conn.commit()
        logging.info(f"💾 Oferta wynajmu {listing_id} ZAPISANA w bazie pod ID {created_offer_id} (COMMIT wykonany)")

        return created_offer_id

    except Exception as error:
        logging.exception(f"❌ Error during inserting new rental listing {offer_data.get('listing_id', 'Unknown')}: {error}")
        try:
            conn.rollback()
            logging.warning(f"🔄 Transaction rolled back for offer {offer_data.get('listing_id', 'Unknown')}")
        except Exception as rollback_error:
            logging.error(f"Error during rollback: {rollback_error}")
        return None


def update_rental_price_in_listings_table(offer_data, cur):
    """Update rental price in apartments_rental_listings table"""
    try:
        id, new_price, new_price_per_m = offer_data

        update_price_query = """
            UPDATE apartments_rental_listings
            SET updated_rental_price = %s, updated_rental_price_per_m = %s
            WHERE id = %s
            ;"""

        update_price_values = (new_price, new_price_per_m, id)
        cur.execute(update_price_query, update_price_values)
        
        logging.debug(f"BAZA: update oferty wynajmu {id} w apartments_rental_listings: nowa cena - {new_price}, nowa cena za m2 - {new_price_per_m}")
    except Exception as error:
        logging.exception(f"Error during updating rental price in listings_table: {error}")


def update_rental_price_in_history_table(offer_data, cur):
    """Update rental price history"""
    try:
        id, new_price, _ = offer_data

        change_date = datetime.date.today()

        old_price_query = """
            SELECT rental_price
            FROM apartments_rental_listings
            WHERE id = %s
            ;"""
        cur.execute(old_price_query, (id,))
        old_price = cur.fetchone()[0]

        insert_history_query = """
            INSERT INTO rental_price_history (listing_id, old_price, new_price, change_date)
            VALUES (%s, %s, %s, %s )
            RETURNING id
            ;"""
        
        update_history_values = (id, old_price, new_price, change_date)
        cur.execute(insert_history_query, update_history_values)
        id_history_table = cur.fetchone()[0]

        logging.debug(f"BAZA: update oferty wynajmu {id} w rental_price_history pod id {id_history_table}")

    except Exception as error:
        logging.exception(f"Error during updating rental price in rental_price_history table: {error}")


def update_active_rental_offers(offer_data, conn, cur):
    """Update active rental offers with new price - SAVES IMMEDIATELY TO DATABASE"""
    try:
        id_db, new_price, new_price_per_m = offer_data
        logging.debug(f"Aktualizuję cenę oferty ID {id_db}: {new_price} PLN, {new_price_per_m} PLN/m2")

        update_rental_price_in_listings_table(offer_data, cur)
        update_rental_price_in_history_table(offer_data, cur)

        # NATYCHMIASTOWY COMMIT DO BAZY DANYCH
        conn.commit()
        logging.info(f"💾 Cena oferty ID {id_db} ZAKTUALIZOWANA w bazie (COMMIT wykonany)")

    except Exception as error:
        logging.exception(f"❌ Error during updating active rental offers: {error}")
        try:
            conn.rollback()
            logging.warning(f"🔄 Transaction rolled back for price update")
        except Exception as rollback_error:
            logging.error(f"Error during rollback: {rollback_error}")
 

def update_deleted_rental_offers(offer_data, conn, cur):
    """Mark rental offers as deleted/inactive - SAVES IMMEDIATELY TO DATABASE"""
    try:
        update_inactive_query = """
        UPDATE apartments_rental_listings
        SET active = %s, closing_date = %s
        WHERE id = %s
        ;"""

        current_date = datetime.date.today()
        id_db = offer_data[0]
        listing_id = offer_data[1] if len(offer_data) > 1 else "Unknown"

        logging.debug(f"Oznaczam ofertę {listing_id} (ID: {id_db}) jako nieaktywną z datą {current_date}")

        update_inactive_values = (False, current_date, id_db)
        cur.execute(update_inactive_query, update_inactive_values)

        # NATYCHMIASTOWY COMMIT DO BAZY DANYCH
        conn.commit()
        logging.info(f"💾 Oferta {listing_id} (ID: {id_db}) oznaczona jako NIEAKTYWNA w bazie (COMMIT wykonany)")

    except Exception as error:
        logging.exception(f"❌ Error during updating deleted rental offers: {error}")
        try:
            conn.rollback()
            logging.warning(f"🔄 Transaction rolled back for deleted offer update")
        except Exception as rollback_error:
            logging.error(f"Error during rollback: {rollback_error}")
