#!/usr/bin/env python3
"""
Debug script to identify SQL placeholder mismatch in rental database operations.
"""

def debug_sql_mismatch():
    # SQL query from the rental_db_operations.py
    listing_query = """
        INSERT INTO apartments_rental_listings (otodom_listing_id, title, market, advert_type, 
        creation_date, creation_time, pushed_ap_at, exclusive_offer, creation_source, description_text, 
        area, rental_price, updated_rental_price, rental_price_per_m, updated_rental_price_per_m, 
        deposit_amount, utilities_included, location_id, street, additional_rent, 
        rooms_num, floor_num, heating, ownership, proper_type, construction_status, energy_certificate, 
        building_build_year, building_floors_num,  building_material, building_type, windows_type,  
        local_plan_url, video_url, view3d_url, walkaround_url, owner_id, owner_name, agency_id, 
        agency_name, offer_link, active, closing_date)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        RETURNING id
        ;"""
    
    # Count placeholders
    placeholder_count = listing_query.count('%s')
    print(f"Number of %s placeholders: {placeholder_count}")
    
    # Extract column names from the INSERT statement
    import re
    columns_match = re.search(r'\((.*?)\)\s*VALUES', listing_query, re.DOTALL)
    if columns_match:
        columns_text = columns_match.group(1)
        columns = [col.strip() for col in columns_text.split(',')]
        print(f"Number of columns: {len(columns)}")
        print("Columns:")
        for i, col in enumerate(columns, 1):
            print(f"  {i:2d}. {col}")
    
    # List the values that would be passed
    print("\nValues that would be passed:")
    value_fields = [
        "offer_data['listing_id']",
        "offer_data['title']", 
        "offer_data['market']",
        "offer_data['advert_type']",
        "offer_data['creation_date']",
        "offer_data['creation_time']",
        "offer_data['pushed_ap_at']",
        "offer_data['exclusive_offer']",
        "offer_data['creation_source']",
        "offer_data['description_text']",
        "offer_data['area']",
        "offer_data['rental_price']",
        "offer_data['rental_price']", # duplicate for updated_rental_price
        "offer_data['rental_price_per_m']",
        "offer_data['rental_price_per_m']", # duplicate for updated_rental_price_per_m
        "offer_data.get('deposit_amount')",
        "offer_data.get('utilities_included', False)",
        "location_id[0]",
        "offer_data['street']",
        "offer_data.get('additional_rent')",
        "offer_data['rooms_num']",
        "offer_data['floor_num']",
        "offer_data['heating']",
        "offer_data['ownership']",
        "offer_data['proper_type']",
        "offer_data['construction_status']",
        "offer_data['energy_certificate']",
        "offer_data['building_build_year']",
        "offer_data['building_floors_num']",
        "offer_data['building_material']",
        "offer_data['building_type']",
        "offer_data['windows_type']",
        "offer_data['local_plan_url']",
        "offer_data['video_url']",
        "offer_data['view3d_url']",
        "offer_data['walkaround_url']",
        "offer_data['owner_id']",
        "offer_data['owner_name']",
        "offer_data['agency_id']",
        "offer_data['agency_name']",
        "offer_data['offer_link']",
        "offer_data['active']",
        "offer_data['closing_date']"
    ]
    
    print(f"Number of values: {len(value_fields)}")
    for i, field in enumerate(value_fields, 1):
        print(f"  {i:2d}. {field}")
    
    print(f"\nMismatch: Columns={len(columns) if 'columns' in locals() else 'unknown'}, Placeholders={placeholder_count}, Values={len(value_fields)}")

if __name__ == "__main__":
    debug_sql_mismatch()
