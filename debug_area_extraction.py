#!/usr/bin/env python3
"""
Debug script to examine the JSON structure of rental listing pages
to understand why area extraction is failing.

This script:
1. Fetches a sample listing page
2. Extracts and displays the JSON structure
3. Searches for area-related fields
4. Helps identify the correct path for area extraction

Usage:
    python debug_area_extraction.py
"""

import json
import logging
from scraper.rental_fetch_and_parse import fetch_page
from bs4 import BeautifulSoup
from config.logging_config import setup_logger

# Setup logging
logger = setup_logger()

def debug_area_extraction(url):
    """
    Debug area extraction for a specific listing URL.
    
    Args:
        url (str): The listing URL to debug
    """
    
    print("="*80)
    print("DEBUG: AREA EXTRACTION FROM RENTAL LISTING")
    print("="*80)
    print(f"URL: {url}")
    print("="*80)
    
    try:
        # Fetch the page
        response = fetch_page(url)
        if response is None:
            print("❌ Failed to fetch page")
            return
        
        # Parse HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        script_tag = soup.find('script', {'id': '__NEXT_DATA__'})
        
        if not script_tag:
            print("❌ No __NEXT_DATA__ script tag found")
            return
        
        # Parse JSON
        try:
            json_data = json.loads(script_tag.get_text())
        except json.JSONDecodeError as e:
            print(f"❌ JSON parsing error: {e}")
            return
        
        # Extract offer data
        offer_data = json_data.get("props", {}).get("pageProps", {}).get("ad", {})
        
        if not offer_data:
            print("❌ No offer data found in JSON")
            return
        
        print("✅ Successfully extracted JSON data")
        print("\n" + "="*80)
        print("SEARCHING FOR AREA-RELATED FIELDS")
        print("="*80)
        
        # Search for area-related fields
        area_fields = []
        
        def search_for_area(obj, path=""):
            """Recursively search for area-related fields"""
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = f"{path}.{key}" if path else key
                    
                    # Check if key contains area-related terms
                    key_lower = key.lower()
                    if any(term in key_lower for term in ['area', 'surface', 'size', 'meter', 'sqm', 'm2']):
                        area_fields.append((current_path, key, value))
                    
                    # Recursively search in nested objects
                    search_for_area(value, current_path)
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    current_path = f"{path}[{i}]" if path else f"[{i}]"
                    search_for_area(item, current_path)
        
        # Search the entire offer data
        search_for_area(offer_data)
        
        if area_fields:
            print(f"Found {len(area_fields)} area-related fields:")
            print("-" * 80)
            for path, key, value in area_fields:
                print(f"Path: {path}")
                print(f"Key:  {key}")
                print(f"Value: {value}")
                print(f"Type: {type(value)}")
                print("-" * 80)
        else:
            print("❌ No area-related fields found")
        
        # Check specific common paths
        print("\n" + "="*80)
        print("CHECKING COMMON AREA PATHS")
        print("="*80)

        # Safe get function to handle None values
        def safe_get(obj, *keys):
            for key in keys:
                if obj is None:
                    return None
                if isinstance(obj, dict):
                    obj = obj.get(key)
                else:
                    return None
            return obj

        common_paths = [
            ("offer_data.areaInSquareMeters", offer_data.get("areaInSquareMeters")),
            ("offer_data.area", offer_data.get("area")),
            ("offer_data.property.areaInSquareMeters", safe_get(offer_data, "property", "areaInSquareMeters")),
            ("offer_data.property.area", safe_get(offer_data, "property", "area")),
            ("offer_data.characteristics", offer_data.get("characteristics")),
            ("offer_data.target.Area", safe_get(offer_data, "target", "Area")),
        ]

        for path, value in common_paths:
            print(f"{path}: {value}")
        
        # Check characteristics array for area
        characteristics = offer_data.get("characteristics", [])
        if characteristics:
            print(f"\nCharacteristics array has {len(characteristics)} items:")
            for i, char in enumerate(characteristics):
                if isinstance(char, dict):
                    key = char.get("key", "")
                    value = char.get("value", "")
                    label = char.get("label", "")
                    
                    # Check if this characteristic is area-related
                    if any(term in key.lower() for term in ['area', 'surface', 'size', 'meter', 'sqm', 'm2']):
                        print(f"  [{i}] AREA FOUND - Key: {key}, Value: {value}, Label: {label}")
                    elif any(term in str(label).lower() for term in ['powierzchnia', 'metr', 'm2', 'area']):
                        print(f"  [{i}] AREA FOUND - Key: {key}, Value: {value}, Label: {label}")
        
        # Display basic offer info
        print("\n" + "="*80)
        print("BASIC OFFER INFO")
        print("="*80)
        print(f"ID: {offer_data.get('id')}")
        print(f"Title: {offer_data.get('title', '')[:100]}...")
        print(f"Price: {offer_data.get('totalPrice', {}).get('value')}")
        print(f"Direct areaInSquareMeters: {offer_data.get('areaInSquareMeters')}")
        
        # Save full JSON for manual inspection
        with open('debug_offer_data.json', 'w', encoding='utf-8') as f:
            json.dump(offer_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ Full offer data saved to 'debug_offer_data.json' for manual inspection")
        
    except Exception as error:
        logging.exception(f"❌ Error during debugging: {error}")


def main():
    """Main function"""
    
    # Use one of the sample URLs from our test
    sample_urls = [
        "https://www.otodom.pl/pl/oferta/nowe-3-pok-oddzielne-metro-ursynow-balkon-ID4vJtH",
        "https://www.otodom.pl/pl/oferta/mieszkanie-53-m2-dwa-pokoje-ul-bobrowiecka-3a-bezposrednio-ID4vJsR",
        "https://www.otodom.pl/pl/oferta/wyjatkowy-4-pokojowy-apartament-w-sercu-warszawy-ID4vJtm"
    ]
    
    for i, url in enumerate(sample_urls, 1):
        print(f"\n{'='*20} DEBUGGING LISTING {i} {'='*20}")
        debug_area_extraction(url)
        
        if i < len(sample_urls):
            input("\nPress Enter to continue to next listing...")


if __name__ == "__main__":
    main()
