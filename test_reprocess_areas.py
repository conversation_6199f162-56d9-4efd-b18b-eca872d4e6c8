#!/usr/bin/env python3
"""
Test script to reprocess a small sample of rental listings with NULL area values.

This script:
1. Finds a few rental listings with NULL area values
2. Re-fetches and processes them to test the reprocessing logic
3. Shows the results without making permanent changes (optional dry-run mode)

Usage:
    python test_reprocess_areas.py
    python test_reprocess_areas.py --dry-run  # Test without saving to database
"""

import logging
import sys
from datetime import datetime

from db.db_setup import get_db_connection
from db.rental_db_operations import get_listings_with_null_area, update_listing_area
from scraper.rental_fetch_and_parse import fetch_page, download_rental_data_from_listing_page
from scraper.rental_transform_data import transform_rental_data
from config.logging_config import setup_logger

# Setup logging
logger = setup_logger()

def test_reprocess_sample(dry_run=False, max_listings=3):
    """
    Test reprocessing on a small sample of listings.
    
    Args:
        dry_run (bool): If True, don't save changes to database
        max_listings (int): Maximum number of listings to test
    """
    
    print("="*60)
    print("TESTING REPROCESSING OF RENTAL LISTINGS WITH MISSING AREAS")
    print("="*60)
    
    if dry_run:
        print("🧪 DRY RUN MODE - Zmiany nie będą zapisane do bazy danych")
        print("="*60)
    
    start_time = datetime.now()
    logging.info("🧪 Rozpoczynam test reprocessingu ofert z brakującą powierzchnią...")
    
    # Connect to database
    conn = get_db_connection()
    if not conn:
        logging.error("❌ Nie udało się połączyć z bazą danych")
        return
    
    cur = conn.cursor()
    
    try:
        # Get sample of listings with NULL area
        logging.info("🔍 Wyszukuję oferty z brakującą powierzchnią...")
        all_listings = get_listings_with_null_area(cur)
        
        if not all_listings:
            logging.info("✅ Brak ofert z brakującą powierzchnią - wszystko w porządku!")
            return
        
        # Limit to sample size
        sample_listings = all_listings[:max_listings]
        total_listings = len(sample_listings)
        
        logging.info(f"📊 Testuję {total_listings} ofert z {len(all_listings)} dostępnych")
        
        # Process each listing
        successful_tests = 0
        failed_tests = 0
        
        for i, listing_info in enumerate(sample_listings, 1):
            db_id, otodom_listing_id, offer_link, current_area = listing_info
            
            print(f"\n📋 [{i}/{total_listings}] Testuję ofertę ID {db_id} (Otodom: {otodom_listing_id})")
            logging.info(f"🔄 Testuję ofertę ID {db_id}")
            
            try:
                # Fetch the listing page
                response = fetch_page(offer_link)
                if response is None:
                    print(f"   ❌ Nie udało się pobrać strony")
                    failed_tests += 1
                    continue
                
                # Extract data from the page
                offer_data = download_rental_data_from_listing_page(response)
                if offer_data is None:
                    print(f"   ❌ Nie udało się wyodrębnić danych")
                    failed_tests += 1
                    continue
                
                # Transform the data
                transformed_data = transform_rental_data(offer_data)
                if transformed_data is None:
                    print(f"   ❌ Nie udało się przetworzyć danych")
                    failed_tests += 1
                    continue
                
                # Extract area value
                area_value = transformed_data.get('area')
                if area_value is None:
                    print(f"   ⚠️ Brak danych o powierzchni w pobranej ofercie")
                    failed_tests += 1
                    continue
                
                # Validate area value
                try:
                    area_float = float(area_value)
                    if area_float <= 0:
                        print(f"   ⚠️ Nieprawidłowa powierzchnia: {area_float}")
                        failed_tests += 1
                        continue
                except (ValueError, TypeError):
                    print(f"   ⚠️ Nie można przekonwertować powierzchni: '{area_value}'")
                    failed_tests += 1
                    continue
                
                # Show what would be updated
                print(f"   ✅ Znaleziono powierzchnię: {area_float} m²")
                
                if not dry_run:
                    # Update the database
                    success = update_listing_area(db_id, area_float, conn, cur)
                    if success:
                        print(f"   💾 Zaktualizowano w bazie danych")
                        successful_tests += 1
                    else:
                        print(f"   ❌ Błąd podczas aktualizacji bazy danych")
                        failed_tests += 1
                else:
                    print(f"   🧪 DRY RUN - nie zapisuję do bazy")
                    successful_tests += 1
                    
            except Exception as error:
                print(f"   ❌ Nieoczekiwany błąd: {error}")
                logging.exception(f"❌ Błąd podczas testowania oferty ID {db_id}: {error}")
                failed_tests += 1
        
        # Summary
        end_time = datetime.now()
        duration = end_time - start_time
        
        print("\n" + "="*60)
        print("PODSUMOWANIE TESTU")
        print("="*60)
        print(f"📊 Łącznie przetestowano:    {total_listings}")
        print(f"✅ Pomyślne testy:           {successful_tests}")
        print(f"❌ Błędy:                    {failed_tests}")
        print(f"⏱️ Czas wykonania:           {duration}")
        
        if dry_run:
            print("🧪 DRY RUN - żadne zmiany nie zostały zapisane")
        
        print("="*60)
        
        logging.info(f"🧪 Test zakończony!")
        logging.info(f"📊 Statystyki: {successful_tests} sukces, {failed_tests} błędów z {total_listings} łącznie")
        
    except Exception as error:
        logging.exception(f"❌ Nieoczekiwany błąd podczas testu: {error}")
    
    finally:
        # Close database connection
        if cur:
            cur.close()
        if conn:
            conn.close()
        logging.info("🔌 Połączenie z bazą danych zamknięte")


def main():
    """Main function"""
    # Check for dry-run argument
    dry_run = "--dry-run" in sys.argv
    
    test_reprocess_sample(dry_run=dry_run, max_listings=3)


if __name__ == "__main__":
    main()
