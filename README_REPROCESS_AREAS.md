# Reprocessing Rental Listings with Missing Area Values

This documentation explains how to reprocess rental listings that have NULL area values in the database.

## Problem Description

Sometimes during the scraping process, the area (surface area in square meters) of rental listings might not be extracted properly, resulting in NULL values in the database. This can happen due to:

- Temporary network issues during scraping
- Changes in the website structure
- Parsing errors in specific listings
- Server-side issues on Otodom

## Solution Overview

The solution consists of several scripts that:

1. **Check** the current status of listings with missing areas
2. **Test** the reprocessing logic on a small sample
3. **Reprocess** all listings with missing areas

## Scripts Description

### 1. `check_null_areas.py`
**Purpose**: Check the current status of rental listings with NULL area values.

**Usage**:
```bash
python check_null_areas.py
```

**What it does**:
- Connects to the database
- Counts total rental listings
- Counts listings with NULL area values
- Shows sample listings with missing areas
- Displays a summary report

### 2. `test_reprocess_areas.py`
**Purpose**: Test the reprocessing logic on a small sample without making permanent changes.

**Usage**:
```bash
# Test with database updates (max 3 listings)
python test_reprocess_areas.py

# Dry run mode - test without saving to database
python test_reprocess_areas.py --dry-run
```

**What it does**:
- Finds a few listings with NULL areas
- Re-fetches their pages from Otodom
- Extracts area information
- Shows what would be updated
- Optionally saves changes to database

### 3. `reprocess_missing_areas.py`
**Purpose**: Reprocess ALL rental listings with NULL area values.

**Usage**:
```bash
python reprocess_missing_areas.py
```

**What it does**:
- Finds all active listings with NULL areas
- Re-fetches each listing page from Otodom
- Extracts and validates area information
- Updates the database immediately for each listing
- Provides detailed progress and summary reports

## Recommended Workflow

### Step 1: Check Current Status
First, check how many listings have missing areas:

```bash
python check_null_areas.py
```

This will show you:
- Total number of rental listings
- Number of listings with missing areas
- Sample of affected listings

### Step 2: Test the Process
Before running the full reprocessing, test on a small sample:

```bash
# Dry run first to see what would happen
python test_reprocess_areas.py --dry-run

# Then test with actual updates on 3 listings
python test_reprocess_areas.py
```

### Step 3: Run Full Reprocessing
If the test looks good, run the full reprocessing:

```bash
python reprocess_missing_areas.py
```

**⚠️ Important**: This script will process ALL listings with missing areas. Make sure you have:
- A stable internet connection
- Sufficient time (it includes delays between requests)
- Database backup (recommended)

### Step 4: Verify Results
After reprocessing, check the status again:

```bash
python check_null_areas.py
```

## Features

### Safety Features
- **Immediate database commits**: Each successful update is committed immediately
- **Error handling**: Robust error handling for network and parsing issues
- **Validation**: Area values are validated before updating
- **Rollback on errors**: Database transactions are rolled back on errors
- **Rate limiting**: Delays between requests to avoid overwhelming Otodom

### Logging
All scripts provide detailed logging:
- Progress information
- Error details
- Summary statistics
- Database operations

### Data Validation
The scripts validate area values:
- Must be a valid number
- Must be greater than 0
- Must be convertible to float

## Database Changes

The scripts add two new functions to `db/rental_db_operations.py`:

1. `get_listings_with_null_area(cur)` - Retrieves listings with NULL areas
2. `update_listing_area(listing_id, area_value, conn, cur)` - Updates area for a specific listing

## Error Handling

The scripts handle various error scenarios:
- Network connectivity issues
- Invalid or missing data on listing pages
- Database connection problems
- Data validation failures

## Performance Considerations

- **Rate limiting**: 1.5-3 second delays between requests
- **Immediate commits**: Each update is committed immediately to prevent data loss
- **Memory efficient**: Processes one listing at a time
- **Progress tracking**: Detailed progress reporting

## Troubleshooting

### Common Issues

1. **Database connection errors**
   - Check your `.env` file configuration
   - Ensure PostgreSQL is running
   - Verify database credentials

2. **Network errors**
   - Check internet connection
   - Otodom might be temporarily unavailable
   - Consider running during off-peak hours

3. **No area data found**
   - Some listings might genuinely not have area information
   - Website structure might have changed
   - Check the logs for specific error details

### Logs Location
Check the logs for detailed error information and progress tracking.

## Example Output

```
==============================================================
REPROCESSING RENTAL LISTINGS WITH MISSING AREA VALUES
==============================================================

📊 Znaleziono 15 ofert do przetworzenia

📋 [1/15] Przetwarzam ofertę...
🔄 Przetwarzam ofertę ID 123 (Otodom: 65657058)
✅ Oferta ID 123 zaktualizowana: powierzchnia = 45.5 m²

📋 [2/15] Przetwarzam ofertę...
🔄 Przetwarzam ofertę ID 124 (Otodom: 65657059)
✅ Oferta ID 124 zaktualizowana: powierzchnia = 62.0 m²

...

==============================================================
PODSUMOWANIE REPROCESSINGU
==============================================================
📊 Łącznie przetworzono:     15
✅ Pomyślnie zaktualizowano: 13
❌ Błędy:                    2
⏱️ Czas wykonania:           0:02:45
==============================================================
```
