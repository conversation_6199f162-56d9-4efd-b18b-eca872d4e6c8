#!/usr/bin/env python3
"""
Script to reprocess rental listings that have NULL area values.

This script:
1. Finds all active rental listings with NULL area values
2. Re-fetches the listing page for each one
3. Extracts the area information from the page
4. Updates the database with the correct area value

Usage:
    python reprocess_missing_areas.py
"""

import logging
import time
import random
from datetime import datetime

from db.db_setup import get_db_connection
from db.rental_db_operations import get_listings_with_null_area, update_listing_area
from scraper.rental_fetch_and_parse import fetch_page, download_rental_data_from_listing_page
from scraper.rental_transform_data import transform_rental_data
from config.logging_config import setup_logger

# Setup logging
logger = setup_logger()


def reprocess_single_listing(listing_info, conn, cur):
    """
    Reprocess a single listing to extract area information.
    
    Args:
        listing_info (tuple): (id, otodom_listing_id, offer_link, current_area)
        conn: Database connection
        cur: Database cursor
        
    Returns:
        bool: True if successfully updated, False otherwise
    """
    db_id, otodom_listing_id, offer_link, current_area = listing_info
    
    try:
        logging.info(f"🔄 Przetwarzam ofertę ID {db_id} (Otodom: {otodom_listing_id})")
        logging.debug(f"   Link: {offer_link}")
        logging.debug(f"   Obecna powierzchnia: {current_area}")
        
        # Fetch the listing page
        response = fetch_page(offer_link)
        if response is None:
            logging.error(f"❌ Nie udało się pobrać strony dla oferty ID {db_id}")
            return False
        
        # Extract data from the page
        offer_data = download_rental_data_from_listing_page(response)
        if offer_data is None:
            logging.error(f"❌ Nie udało się wyodrębnić danych z oferty ID {db_id}")
            return False
        
        # Transform the data to ensure proper formatting
        transformed_data = transform_rental_data(offer_data)
        if transformed_data is None:
            logging.error(f"❌ Nie udało się przetworzyć danych dla oferty ID {db_id}")
            return False
        
        # Extract area value
        area_value = transformed_data.get('area')
        if area_value is None:
            logging.warning(f"⚠️ Brak danych o powierzchni w pobranej ofercie ID {db_id}")
            return False
        
        # Validate area value
        try:
            area_float = float(area_value)
            if area_float <= 0:
                logging.warning(f"⚠️ Nieprawidłowa powierzchnia ({area_float}) dla oferty ID {db_id}")
                return False
        except (ValueError, TypeError):
            logging.warning(f"⚠️ Nie można przekonwertować powierzchni '{area_value}' dla oferty ID {db_id}")
            return False
        
        # Update the database
        success = update_listing_area(db_id, area_float, conn, cur)
        if success:
            logging.info(f"✅ Oferta ID {db_id} zaktualizowana: powierzchnia = {area_float} m²")
            return True
        else:
            logging.error(f"❌ Nie udało się zaktualizować oferty ID {db_id}")
            return False
            
    except Exception as error:
        logging.exception(f"❌ Nieoczekiwany błąd podczas przetwarzania oferty ID {db_id}: {error}")
        return False


def main():
    """Main function to reprocess listings with missing area values"""
    
    print("="*60)
    print("REPROCESSING RENTAL LISTINGS WITH MISSING AREA VALUES")
    print("="*60)
    
    start_time = datetime.now()
    logging.info("🚀 Rozpoczynam reprocessing ofert z brakującą powierzchnią...")
    
    # Connect to database
    conn = get_db_connection()
    if not conn:
        logging.error("❌ Nie udało się połączyć z bazą danych")
        return
    
    cur = conn.cursor()
    
    try:
        # Get all listings with NULL area
        logging.info("🔍 Wyszukuję oferty z brakującą powierzchnią...")
        listings_with_null_area = get_listings_with_null_area(cur)
        
        if not listings_with_null_area:
            logging.info("✅ Brak ofert z brakującą powierzchnią - wszystko w porządku!")
            return
        
        total_listings = len(listings_with_null_area)
        logging.info(f"📊 Znaleziono {total_listings} ofert do przetworzenia")
        
        # Process each listing
        successful_updates = 0
        failed_updates = 0
        
        for i, listing_info in enumerate(listings_with_null_area, 1):
            logging.info(f"\n📋 [{i}/{total_listings}] Przetwarzam ofertę...")
            
            success = reprocess_single_listing(listing_info, conn, cur)
            
            if success:
                successful_updates += 1
            else:
                failed_updates += 1
            
            # Add delay to avoid overwhelming the server
            if i < total_listings:  # Don't delay after the last item
                delay = random.uniform(1.5, 3.0)
                logging.debug(f"⏳ Czekam {delay:.1f}s przed następną ofertą...")
                time.sleep(delay)
        
        # Summary
        end_time = datetime.now()
        duration = end_time - start_time
        
        print("\n" + "="*60)
        print("PODSUMOWANIE REPROCESSINGU")
        print("="*60)
        print(f"📊 Łącznie przetworzono:     {total_listings}")
        print(f"✅ Pomyślnie zaktualizowano: {successful_updates}")
        print(f"❌ Błędy:                    {failed_updates}")
        print(f"⏱️ Czas wykonania:           {duration}")
        print("="*60)
        
        logging.info(f"🎉 Reprocessing zakończony!")
        logging.info(f"📊 Statystyki: {successful_updates} sukces, {failed_updates} błędów z {total_listings} łącznie")
        logging.info(f"⏱️ Czas wykonania: {duration}")
        
    except Exception as error:
        logging.exception(f"❌ Nieoczekiwany błąd podczas reprocessingu: {error}")
    
    finally:
        # Close database connection
        if cur:
            cur.close()
        if conn:
            conn.close()
        logging.info("🔌 Połączenie z bazą danych zamknięte")


if __name__ == "__main__":
    main()
